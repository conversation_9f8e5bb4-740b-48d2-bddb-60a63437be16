package com.szhr.framework.web.service;

import com.szhr.common.constant.SmsConstants;
import jakarta.annotation.Resource;
import org.dromara.email.jakarta.api.MailClient;
import org.dromara.email.jakarta.comm.entity.MailMessage;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * 推送服务
 *
 * <AUTHOR>
 */
@Component
public class PushService {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${send.switch}")
    private String sendSwitch;

    @Resource
    private MailClient mailClient;

    /**
     * 发送短信
     * @param mobilePhone 手机号
     * @param templateCode  （阿里大于预先定义好的模板）
     * @param params 参数
     * @return true
     */
    public Boolean sendBySMS(String mobilePhone, String templateCode, LinkedHashMap<String, String> params) {
        try {
            if ("off".equals(sendSwitch)) {
                log.warn("发送开关关闭，不发送");
                return false;
            }
            log.debug("========sendBySMS:{},{},{}",mobilePhone, templateCode, params);
            SmsFactory.getSmsBlend(SmsConstants.SMS_BLENDS_ALIYUN_IUCAI)
                    .sendMessage(mobilePhone, templateCode, params);
        } catch (Exception e) {
            log.error("发送短信出现异常：", e);
            return false;
        }

        return true;
    }

    /**
     * 邮件发送
     * @param to
     * @param subject
     * @param content
     */
    public void sendMail(String to, String subject, String content) {
        MailMessage.MailsBuilder builder = MailMessage.Builder();
        MailMessage mailMessage = builder.mailAddress(to)
                .title(subject)
                .htmlContent(content)
                .build();
        mailClient.send(mailMessage);
    }
}
