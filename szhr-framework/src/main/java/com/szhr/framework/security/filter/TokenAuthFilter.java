package com.szhr.framework.security.filter;

import com.szhr.common.constant.ThreadLocalConstants;
import com.szhr.common.core.domain.model.LoginFairUser;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.ThreadLocalUtil;
import com.szhr.framework.security.context.FairUserRequestContext;
import com.szhr.framework.web.service.FrontTokenService;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class TokenAuthFilter implements Filter {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final FairUserRequestContext fairUserRequestContext;

    private final FrontTokenService frontTokenService;

    // 构造函数注入依赖
    public TokenAuthFilter(FairUserRequestContext fairUserRequestContext, FrontTokenService frontTokenService) {
        this.fairUserRequestContext = fairUserRequestContext;
        this.frontTokenService = frontTokenService;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String uri = request.getRequestURI();
        logger.debug("front request Url: {}", uri);

        // 如果当前请求的 URI 在排除列表中，则跳过过滤器逻辑
        if (uri.startsWith("/zzxc/front/ei/fairUser/smslogin")) {
            logger.debug("TokenAuthFilter: front request smslogin: {}", uri);
            chain.doFilter(request, response);
            return;
        }

        if (uri.startsWith("/zzxc/front/ei/")) {
            logger.debug("uri.startsWith: /zzxc/front/ei/: {}", uri);

            String auth = request.getHeader("Authorization");
            if (StringUtils.isBlank(auth) || !StringUtils.startsWith(auth,"Bearer ")) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "缺少Token");
                return;
            }

            LoginFairUser loginFairUser = frontTokenService.getLoginFairUser(request);
            if (loginFairUser == null) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "无效Token");
                return;
            }

            fairUserRequestContext.setFairUserId(loginFairUser.getFairUserId());
            fairUserRequestContext.setActivityId(loginFairUser.getActivityId());
        }

        chain.doFilter(request, response);
    }
}

