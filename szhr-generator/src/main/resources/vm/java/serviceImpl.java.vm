package ${packageName}.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    #foreach ($column in $columns)
        #if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
        import com.rchuing.common.utils.DateUtils;
            #break
        #end
    #end
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {
    @Resource
    private ${ClassName}Mapper ${className}Mapper;
}
