package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@TableName(value = "${tableName}", autoResultMap = true)
public class ${ClassName} implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
    #if($column.list)
        #set($parentheseIndex=$column.columnComment.indexOf("（"))
        #if($parentheseIndex != -1)
            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
        #else
            #set($comment=$column.columnComment)
        #end
        #if($parentheseIndex != -1)
    @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()" )
        #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
        #else
    @Excel(name = "${comment}" )
        #end
    #end
    #if($column.isPk == 1)
    @TableId(value = "$column.columnName" , type = IdType.INPUT)
    #else
    @TableField(value = "$column.columnName")
    #end
    private $column.javaType $column.javaField;

    #end
#end
}
