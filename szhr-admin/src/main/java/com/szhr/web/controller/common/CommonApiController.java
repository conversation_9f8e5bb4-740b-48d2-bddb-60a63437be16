package com.szhr.web.controller.common;

import com.szhr.common.constant.CacheConstants;
import com.szhr.common.constant.Constants;
import com.szhr.common.constant.SmsConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.redis.RedisCache;
import com.szhr.common.exception.user.CaptchaException;
import com.szhr.common.exception.user.CaptchaExpireException;
import com.szhr.common.utils.StringUtils;
import com.szhr.ei.converter.request.SendSmsParams;
import com.szhr.framework.web.service.PushService;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 通用Api接口
 */
@RestController
@RequestMapping("/common/api")
public class CommonApiController extends BaseController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private PushService pushService;

    /**
     * 发送短信验证码
     * 请求示例：
     * {
     *     "mobilePhone": "15976863927",
     *     "code": "n3fq",
     *     "uuid": "ddba97eb-28c6-46d0-bcfe-ac58d2f56e0a"
     * }
     * 响应示例：
     * {
     *     "code": "",
     *     "data": null,
     *     "message": "ok",
     *     "redirect": "",
     *     "status": "success"
     * }
     * @param sendSmsParams
     * @return
     */
    @PostMapping("/sendSms")
    public AjaxResult sendSms(@Validated @RequestBody(required = true) SendSmsParams sendSmsParams) {
        //logger.debug("发送短信验证码:{}", JSON.toJSONString(sendSmsParams));

        AjaxResult valret = null;
        String code = sendSmsParams.getCode();
        String uuid = sendSmsParams.getUuid();
        String mobilePhone = sendSmsParams.getMobilePhone();

        // 检查用户输入的图片验证码是否正确
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        redisCache.deleteObject(verifyKey);
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }

        String verifyKeySms = CacheConstants.MOBILE_PHONE_KEY + mobilePhone;
        String smsSendCodeRedis = redisCache.getCacheObject(verifyKeySms);
        if (!StringUtils.isBlank(smsSendCodeRedis)) {
            return error("短信验证码已发送，5分钟内未使用请勿重复请求发送。");
        }

        String smsSendCode = RandomStringUtils.randomNumeric(SmsConstants.SMS_SEND_LENGTH);
        if (StringUtils.isBlank(smsSendCode)) {
            return error("系统错误");
        }

        //发送短信验证码
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("code", smsSendCode);
        pushService.sendBySMS(mobilePhone, SmsConstants.VERIFY_CODE, params);
        redisCache.setCacheObject(verifyKeySms, smsSendCode, Constants.SMS_CODE_EXPIRATION, TimeUnit.MINUTES);
        return success("短信验证码发送成功");
    }



}
