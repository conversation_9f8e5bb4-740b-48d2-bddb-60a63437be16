# 项目相关配置
szhr:
  # 名称
  name: szhr_zzxc
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/szhr/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/szhr/zzxc/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: char

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 28080
  servlet:
    # 应用的访问路径
    context-path: /zzxc
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.szhr: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profileActive@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: ea1602a241a911f0913c0050568e649f
  # 令牌有效期（默认30分钟）（测试改成七天）
  expireTime: 10080

# token配置
token_front:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: dbf6761a698d473396535fb7581890b6
  # 令牌有效期（默认30分钟）（测试改成七天）
  expireTime: 10080

# MyBatis-plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.szhr.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '招展与行程管理'
      paths-to-match: '/**'
      packages-to-scan: com.szhr.ei.controller

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

sms:
  config-type: yaml
  blends:
    aliyun-iucai:
      supplier: alibaba
      #阿里云的accessKey
      accessKeyId: LTAI5tReK3UnXxST6cYgMH6b
      #阿里云的accessKeySecret
      accessKeySecret: ******************************
      #短信签名
      signature: 深i优才
