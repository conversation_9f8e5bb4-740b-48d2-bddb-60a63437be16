# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        url: ******************************************************************************************************************************************************
        username: root
        password: Szhr-Cloud123
        druid:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

    # redis 配置
    data:
        redis:

            sentinel:
                master: mymaster
                nodes:
                    - 172.16.5.188:26379
                    - 172.16.5.188:26380
            database: 0
            password: szhrUdp@629
            timeout: 5000
            lettuce:
                pool:
                    max-active: 8 # 连接池最大连接数
                    max-idle: 8 # 连接池最大空闲连接数
                    min-idle: 0 # 连接池最小空闲连接数
                    max-wait: -1ms # 连接池最大阻塞等待时间
                shutdown-timeout: 100ms # 关闭超时时间

# Minio配置
minio:
    url: http://172.16.5.188:9000 #minio的API端口
    accessKey: minioadmin      #账号
    secretKey: minioadmin      #密码
    bucketName: zzxc          #桶名

# 推送服务
send:
    switch: true