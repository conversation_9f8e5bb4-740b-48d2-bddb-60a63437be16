package com.szhr.quartz.task;

import org.springframework.stereotype.Component;
import com.szhr.common.utils.StringUtils;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask {
    public void ryMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i) {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params) {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams() {
        System.out.println("执行无参方法");
    }
}
