package com.szhr.common.config;

import org.dromara.email.jakarta.api.MailClient;
import org.dromara.email.jakarta.comm.config.MailSmtpConfig;
import org.dromara.email.jakarta.core.factory.MailFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025-07-11 16:44
 */
@Configuration
public class MailConfig {
    @Bean("szhrMailClient")
    public MailClient mailClient() {
        MailSmtpConfig mailSmtpConfig = MailSmtpConfig.builder()
                .smtpServer("smtp.qiye.163.com")
                .port("25")
                .fromAddress("<EMAIL>")
                .username("<EMAIL>")
                .password("Hryhb,1lpa.hr4qJ")
                .isSSL("false")
                .build();
        MailFactory.put("szhrMail", mailSmtpConfig);
        return MailFactory.createMailClient("szhrMail");
    }
}
