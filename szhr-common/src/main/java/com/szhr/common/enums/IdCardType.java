package com.szhr.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum IdCardType {

    ID_CARD("01", "居民身份证"),
    HK_MAC_PERMIT("02", "港澳居民来往内地通行证");
    /*TAIWAN("03", "台胞证"),
    PASSPORT("04", "护照");*/

    private final String code;
    private final String dictName;

    private static final Map<String, String> NAME_TO_CODE_MAP = new HashMap<>();

    static {
        for (IdCardType type : IdCardType.values()) {
            NAME_TO_CODE_MAP.put(type.getDictName(), type.getCode());
        }
    }

    IdCardType (String code, String dictName) {
        this.code = code;
        this.dictName = dictName;
    }

    public String getCode() {
        return code;
    }

    public String getDictName() {
        return dictName;
    }

    /**
     * 使用缓存方式快速获取 code
     */
    public static String getCodeByDictNameFast(String dictName) {
        String code = NAME_TO_CODE_MAP.get(dictName);
        if (code == null) {
            throw new IllegalArgumentException("未找到对应的证件类型: " + dictName);
        }
        return code;
    }
}
