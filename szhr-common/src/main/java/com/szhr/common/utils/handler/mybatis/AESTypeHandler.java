package com.szhr.common.utils.handler.mybatis;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;

/**
 * <AUTHOR>
 * @date 2025-06-12 16:48
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
public class AESTypeHandler extends BaseTypeHandler<String> {
    private static final Logger logger = LoggerFactory.getLogger(AESTypeHandler.class);
    private static final AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, "j92^7K@587654321".getBytes());
    /**
     * 加密
     * @param plainText
     * @return 16进制字符串
     */
    public static String encrypt(String plainText) {
        try {
            return HexUtil.encodeHexStr(aes.encrypt(plainText), false);
        } catch (Exception e) {
            logger.error("加密字符串:{0},产生异常:{}", plainText, e);
        }
        return plainText;
    }

    /**
     * 解密
     * @param hexCipherText 16进制字符串
     * @return 明文
     */
    public static String decrypt(String hexCipherText) {
        if(StringUtils.isEmpty(StringUtils.trim(hexCipherText))) return hexCipherText;
        try {
            return aes.decryptStr(HexUtil.decodeHex(hexCipherText));
        } catch (Exception e) {
            logger.error("解密字符串:{0},产生异常:{}", hexCipherText, e);
        }
        return hexCipherText;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps,
                                    int i, String parameter, JdbcType jdbcType) throws SQLException {
        if (null == parameter) {
            ps.setNull(i, Types.VARCHAR);
        } else {
            ps.setString(i, AESTypeHandler.encrypt(parameter));
        }
    }


    /**
     * 用于在Mybatis获取数据结果集时如何把数据库类型转换为对应的Java类型
     *
     * @param rs         当前的结果集
     * @param columnName 当前的字段名称
     * @return 转换后的Java对象
     */
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String r = rs.getString(columnName);
        return r == null ? null : AESTypeHandler.decrypt(r);
    }

    /**
     * 用于在Mybatis通过字段位置获取字段数据时把数据库类型转换为对应的Java类型
     *
     * @param rs          当前的结果集
     * @param columnIndex 当前字段的位置
     * @return 转换后的Java对象
     */
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String r = rs.getString(columnIndex);
        return r == null ? null : AESTypeHandler.decrypt(r);
    }

    /**
     * 用于Mybatis在调用存储过程后把数据库类型的数据转换为对应的Java类型
     *
     * @param cs          当前的CallableStatement执行后的CallableStatement
     * @param columnIndex 当前输出参数的位置
     * @return 返回解密后的数据
     */
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String r = cs.getString(columnIndex);
        return r == null ? null : AESTypeHandler.decrypt(r);
    }
}
