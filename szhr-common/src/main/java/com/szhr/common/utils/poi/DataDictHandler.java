package com.szhr.common.utils.poi;

import com.alibaba.fastjson2.JSONObject;
import com.szhr.common.utils.DataDictUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;

public class DataDictHandler implements ExcelHandlerAdapter {


  @Override
  public Object format(Object value, String[] args, Cell cell, Workbook wb) {

    String dictType = args[0];
    String dictValue = (value != null) ? value.toString() : "";

    JSONObject dict = DataDictUtils.getDict(dictType);
    if (dict != null) {
      return dict.getString(dictValue);
    }
    return dictValue;
  }
}
