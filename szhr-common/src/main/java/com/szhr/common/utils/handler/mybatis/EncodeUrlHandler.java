package com.szhr.common.utils.handler.mybatis;

import com.szhr.common.utils.AESUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

public class EncodeUrlHandler extends BaseTypeHandler<String> {

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType)
			throws SQLException {
		// 存数据时不加密，直接存储原始值
		ps.setString(i, parameter);
	}

	@Override
	public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
		// 取数据时加密
		String rawValue = rs.getString(columnName);
		try {
			return rawValue != null ? AESUtils.encrypt(rawValue) : null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		String rawValue = rs.getString(columnIndex);
		try {
			return rawValue != null ? AESUtils.encrypt(rawValue) : null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		String rawValue = cs.getString(columnIndex);
		try {
			return rawValue != null ? AESUtils.encrypt(rawValue) : null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}