package com.szhr.common.utils;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Random;

public class AESUtils {


    /**
     * 加解密统一编码方式
     */
    private final static String ENCODING = "utf-8";

    /**
     * 加解密方式
     */
    private final static String ALGORITHM = "AES";

    /**
     * 加密模式及填充方式
     */
    private final static String PATTERN = "AES/ECB/pkcs5padding";

    /**
     * 秘钥生成来源
     */
    public static final String ALLCHAR = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 默认密钥 必须是16位
     */
    public static final String DEFAULT_KEY = "9Rqlsl5I5VE75IrZ";

    /**
     * AES加密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encrypt(String plainText) throws Exception {
        if (plainText == null) {
            return null;
        }
        if (plainText.isEmpty()) {
            return plainText;
        }
        SecretKey secretKey = new SecretKeySpec(DEFAULT_KEY.getBytes(ENCODING), ALGORITHM);
        // AES加密采用pkcs5padding填充
        Cipher cipher = Cipher.getInstance(PATTERN);
        //用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        //执行加密操作
        byte[] encryptData = cipher.doFinal(plainText.getBytes(ENCODING));
        return Hex.encodeHexString(encryptData);
    }


    /**
     * AES解密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String decrypt(String plainText) throws Exception {
        if (plainText == null) {
            return null;
        }
        SecretKey secretKey = new SecretKeySpec(DEFAULT_KEY.getBytes(ENCODING), ALGORITHM);
        // 获取 AES 密码器
        Cipher cipher = Cipher.getInstance(PATTERN);
        // 初始化密码器（解密模型）
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 解密数据, 返回明文
        byte[] encryptData = cipher.doFinal(Hex.decodeHex(plainText.toCharArray()));
        return new String(encryptData, ENCODING);
    }

    /**
     * 生成AES密钥对象
     *
     */
    public static String generateAESKey() {
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < 16; i++) {
            sb.append(ALLCHAR.charAt(random.nextInt(ALLCHAR.length())));
        }
        return sb.toString();
    }


    /**
     * AES加密
     *
     * @param plainText
     * @param key
     * @return
     * @throws Exception
     */
    public static String encrypt(String plainText, String key) throws Exception {
        if (key == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (key.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        if (plainText == null) {
            return null;
        }
        SecretKey secretKey = new SecretKeySpec(key.getBytes(ENCODING), ALGORITHM);
        // AES加密采用pkcs5padding填充
        Cipher cipher = Cipher.getInstance(PATTERN);
        //用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        //执行加密操作
        byte[] encryptData = cipher.doFinal(plainText.getBytes(ENCODING));
        return Hex.encodeHexString(encryptData);
    }


    /**
     * AES解密
     *
     * @param plainText
     * @param key
     * @return
     * @throws Exception
     */
    public static String decrypt(String plainText, String key) throws Exception {
        if (plainText == null) {
            return null;
        }
        SecretKey secretKey = new SecretKeySpec(key.getBytes(ENCODING), ALGORITHM);
        // 获取 AES 密码器
        Cipher cipher = Cipher.getInstance(PATTERN);
        // 初始化密码器（解密模型）
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 解密数据, 返回明文
        byte[] encryptData = cipher.doFinal(Hex.decodeHex(plainText.toCharArray()));
        return new String(encryptData, ENCODING);
    }

}
