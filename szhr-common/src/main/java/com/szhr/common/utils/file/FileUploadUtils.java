package com.szhr.common.utils.file;

import com.szhr.common.config.SzhrConfig;
import com.szhr.common.constant.Constants;
import com.szhr.common.exception.file.FileNameLengthLimitExceededException;
import com.szhr.common.exception.file.FileSizeLimitExceededException;
import com.szhr.common.exception.file.InvalidExtensionException;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
public class FileUploadUtils {
    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 以默认BucketName配置上传到Minio服务器
     *
     * @param file 上传的文件
     *
     * @return 文件名称
     *
     * @throws Exception
     */
    public static String uploadMinio(String prefixName, MultipartFile file, String[] allowedExtension) throws IOException {
        try {
            return uploadMinino(prefixName, file, allowedExtension);
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static String uploadMinio(String prefixName, MultipartFile file) throws IOException {
        try {
            return uploadMinino(prefixName, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 自定义bucketName配置上传到Minio服务器
     *
     * @param file 上传的文件
     *
     * @return 文件名称
     *
     * @throws Exception
     */
    public static String uploadMinio(MultipartFile file, String prefixName) throws IOException {
        try {
            return uploadMinino(prefixName, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static String uploadMinino(String prefixName,
                                      MultipartFile file,
                                      String[] allowedExtension) throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException, InvalidExtensionException {
        int fileNameLength = file.getOriginalFilename().length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }
        assertAllowed(file, allowedExtension);
        try {
            String fileName = extractFilename(prefixName, file);
            return MinioUtil.uploadFile(fileName, file);
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 编码文件名
     */
    /*public static String extractFilename(MultipartFile file) {
        return StringUtils.format("{}/{}.{}",
                DateUtils.datePath(),
                IdUtils.fastSimpleUUID(),
                Seq.getId(Seq.uploadSeqType),
                getExtension(file));
    }*/

    /**
     * 编码文件名
     */
    public static String extractFilename(String prefixName, MultipartFile file) {
        return StringUtils.format("{}/{}/{}.{}",
                prefixName,
                DateUtils.datePath(),
                IdUtils.fastSimpleUUID(),
                getExtension(file));
    }

    public static File getAbsoluteFile(String uploadDir,
                                       String fileName) {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }

    public static String getPathFileName(String uploadDir,
                                         String fileName) {
        int dirLastIndex = SzhrConfig.getProfile().length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        return Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     *
     * @return
     *
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException
     */
    public static void assertAllowed(MultipartFile file,
                                     String[] allowedExtension) throws FileSizeLimitExceededException, InvalidExtensionException {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE) {
            throw new FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension, fileName);
            }
            else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension, fileName);
            }
            else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension, fileName);
            }
            else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION) {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension, fileName);
            }
            else {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension
     * @param allowedExtension
     *
     * @return
     */
    public static boolean isAllowedExtension(String extension,
                                             String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     *
     * @return 后缀名
     */
    public static String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }
}