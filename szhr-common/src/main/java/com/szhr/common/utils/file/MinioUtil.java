package com.szhr.common.utils.file;

import com.szhr.common.config.MinioConfig;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.spring.SpringUtils;
import io.minio.*;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Minio 文件存储工具类
 *
 * <AUTHOR>
 */
public class MinioUtil {
    private static final String bucketName = MinioConfig.getBucketName();

    /**
     * 上传文件
     * @param objectName
     *
     * @throws IOException
     */
    public static String uploadFile(String objectName,
                                    MultipartFile multipartFile) throws IOException {
        if (isNotSafeObjectName(objectName)) {
            throw new IOException("非法的文件对象名称");
        }
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try (InputStream inputStream = multipartFile.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, multipartFile.getSize(), -1)
                    .contentType(multipartFile.getContentType())
                    .build());
            return objectName;
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static InputStream getObject(String objectName) throws IOException {
        if (isNotSafeObjectName(objectName)) {
            throw new IOException("非法的文件对象名称");
        }

        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
        }
        catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 删除单个文件
     * @param objectName 文件路径
     * @return 操作结果
     */
    public static void removeObject(String objectName) throws IOException {
        if (isNotSafeObjectName(objectName)) {
            throw new IOException("非法的文件对象名称");
        }
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 批量删除文件
     * @param objectNames 文件路径列表
     */
    public static void removeObjects(List<String> objectNames) throws IOException {
        if (objectNames == null ) {
            throw new IOException("没有需要删除的文件");
        }
        for (String objectName : objectNames) {
            if (isNotSafeObjectName(objectName)) {
                throw new IOException("非法的文件对象名称");
            }
        }
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try {
            // 转换为DeleteObject列表
            List<DeleteObject> objects = objectNames.stream()
                    .map(DeleteObject::new)
                    .collect(Collectors.toList());

            // 执行批量删除
            Iterable<Result<DeleteError>> results = minioClient.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(bucketName)
                            .objects(objects)
                            .build());
            //MinIO 的批量删除操作 (removeObjects()) 需要显式遍历结果并调用 result.get() 才能真正执行删除操作
            // 必须遍历结果才能发现删除失败的对象
            for (Result<DeleteError> result : results) {
                try {
                    DeleteError error = result.get(); // 这里才会抛出具体错误
                    System.err.println("删除失败: " + error.objectName() + " - 原因: " + error.message());
                } catch (Exception e) {
                    throw new IOException(e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 验证对象名是否安全
     */
    public static boolean isNotSafeObjectName(String objectName) {
        if (StringUtils.isEmpty(objectName)) {
            return true;
        } else if (objectName.contains("../") || objectName.contains("./")) {
            return true;
        } else if (objectName.matches(".*[<>:\"\\|?*].*")) {
            return true;
        } else {
            return false;
        }
    }

    public static String getContentTypeByObjectName(String objectName) {
        int dotIndex = objectName.lastIndexOf('.');
        if (dotIndex < 0) {
            return "application/octet-stream";
        }
        String ext = objectName.substring(dotIndex + 1).toLowerCase();
        switch (ext) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
                return "image/" + ext;
        }
        return "application/octet-stream";
    }
}