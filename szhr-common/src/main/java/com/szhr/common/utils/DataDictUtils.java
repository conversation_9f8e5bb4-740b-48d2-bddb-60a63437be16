package com.szhr.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.szhr.common.core.redis.RedisCache;
import com.szhr.common.utils.spring.SpringUtils;

public class DataDictUtils {

  private static final RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
  private static final String PRE_KEY = "SZHR_IUCAI_MAP_SZHR_IUCAI_DICT_";

  public static JSONObject getDict(String dictType) {
    return redisCache.getCacheObject(PRE_KEY + dictType);
  }

  public static String[] getDictValues(String dictType) {
    JSONObject dict = getDict(dictType);
    if (dict != null) {
      String[] values = dict.values().toArray(new String[0]);
      return values;
    }
    return null;
  }
}
