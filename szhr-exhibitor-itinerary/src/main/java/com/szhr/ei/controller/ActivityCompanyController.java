package com.szhr.ei.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DataDictUtils;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.ActivityCompanyParams;
import com.szhr.ei.converter.response.ActivityCompanyVO;
import com.szhr.ei.domain.ActivityCompany;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.service.IActivityCompanyService;
import com.szhr.ei.service.IActivityInvitationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 招展行程活动单位信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/activityCompany")
@Tag(name = "活动单位信息")
public class ActivityCompanyController extends BaseController {

    @Resource
    private IActivityCompanyService activityCompanyService;

    @Resource
    private IActivityInvitationService activityInvitationService;

    /**
     * 获取活动附件列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activityCompany:list')")
    @GetMapping("/list")
    @Operation(summary = "查询单位列表")
    public TableDataInfo list(ActivityCompanyParams activityCompany) {
        startPage();
        return getDataTable(activityCompanyService.selectActivityList(activityCompany));
    }

    @PreAuthorize("@ss.hasPermi('ei:activityCompany:add')")
    @Log(title = "新增单位信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增单位信息")
    public AjaxResult add(@Validated @RequestBody ActivityCompany activityCompany) {
        //判断单位是否存在
        LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityCompany::getCompanyName, activityCompany.getCompanyName())
                .isNull(ActivityCompany::getDeleteDt);
        if (activityCompanyService.count(wrapper) > 0) {
            return error("该单位名称已存在");
        }
        //判断手机号码是否存在
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityCompany::getMobilePhone, AESTypeHandler.encrypt(activityCompany.getMobilePhone()))
                .isNull(ActivityCompany::getDeleteDt);
        if (activityCompanyService.count(wrapper) > 0) {
            return error("该手机号码已存在");
        }
        activityCompany.setId(IdUtils.randomUUID());
        activityCompany.setCreateDt(DateUtils.getNowDate());
        activityCompany.setCreatedBy(getUserId());
        activityCompany.setLastUpdateDt(activityCompany.getCreateDt());
        activityCompany.setLastUpdatedBy(activityCompany.getCreatedBy());
        return toAjax(activityCompanyService.save(activityCompany));
    }

    @PreAuthorize("@ss.hasPermi('ei:activityCompany:update')")
    @Log(title = "更新单位信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新新增单位")
    public AjaxResult update(@Validated @RequestBody ActivityCompany activityCompany) {
        ActivityCompany originalCompany = activityCompanyService.getById(activityCompany.getId());
        if (originalCompany == null) {
            return error("单位不存在！");
        }
        if (!StringUtils.equals(originalCompany.getCompanyName(), activityCompany.getCompanyName())) {
            //通过单位名称判断单位是否存在
            LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getCompanyName, activityCompany.getCompanyName())
                    .isNull(ActivityCompany::getDeleteDt);
            if (activityCompanyService.count(wrapper) > 0) {
                return error("修改的单位名称已存在！");
            }
        }
        if (!StringUtils.equals(originalCompany.getMobilePhone(), activityCompany.getMobilePhone())) {
            //通过手机号码判断手机号码是否存在
            LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getMobilePhone, AESTypeHandler.encrypt(activityCompany.getMobilePhone()))
                    .isNull(ActivityCompany::getDeleteDt);
            if (activityCompanyService.count(wrapper) > 0) {
                return error("修改的手机号码已存在！");
            }
        }
        activityCompany.setLastUpdateDt(DateUtils.getNowDate());
        activityCompany.setLastUpdatedBy(getUserId());
        return toAjax(activityCompanyService.updateById(activityCompany));
    }

    /**
     * 获取招展行程活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('ei:activityCompany:query')")
    @GetMapping(value = "/query/{id}")
    @Operation(summary = "获取活动单位详情")
    public AjaxResult query(@PathVariable("id") String id) {
        return success(activityCompanyService.getById(id));
    }

    @Operation(summary = "下载单位导入模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String[] fields = {"companyName", "industry", "companyType", "companyProp", "companyScale", "contactPerson", "mobilePhone", "email"};
        ExcelUtil<ActivityCompany> util = new ExcelUtil<>(ActivityCompany.class);
        util.showColumn(fields);
        util.importTemplateInit(response, "单位信息导入");
      util.setDropdownValidation(DataDictUtils.getDictValues("INDUSTRY"), 1, 1, 3000);
        util.setDropdownValidation(DataDictUtils.getDictValues("COMPANY_TYPE"), 2, 1, 3000);
        util.setDropdownValidation(DataDictUtils.getDictValues("COMPANY_PROP"), 3, 1, 3000);
        util.setDropdownValidation(DataDictUtils.getDictValues("COMPANY_SCALE"), 4, 1, 3000);
        util.exportExcel(response);
    }

    @Log(title = "活动单位信息导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('ei:activityCompany:import')")
    @PostMapping("/importData")
    @Operation(summary = "活动单位信息导入")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<ActivityCompany> util = new ExcelUtil<ActivityCompany>(ActivityCompany.class);
        List<ActivityCompany> activityCompanyList = util.importExcel(file.getInputStream());
        if (activityCompanyList == null || activityCompanyList.isEmpty()) {
            return error("导入数据不能为空！");
        }
        Date date = DateUtils.getNowDate();
        List<String> existCompanyName = new ArrayList<>();
        List<String> existMobilePhone = new ArrayList<>();
        for (ActivityCompany activityCompany : activityCompanyList) {
            activityCompany.setId(IdUtils.randomUUID());
            activityCompany.setCreateDt(date);
            activityCompany.setCreatedBy(getUserId());
            //检查公司名称是否已经存在
            LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getCompanyName, activityCompany.getCompanyName())
                    .isNull(ActivityCompany::getDeleteDt);
            if (activityCompanyService.count(wrapper) > 0) {
                existCompanyName.add(activityCompany.getCompanyName());
            }
            //检查手机号码是否已经存在
            wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getMobilePhone, AESTypeHandler.encrypt(activityCompany.getMobilePhone()))
                    .isNull(ActivityCompany::getDeleteDt);
            if (activityCompanyService.count(wrapper) > 0) {
                existMobilePhone.add(activityCompany.getMobilePhone());
            }
        }
        //取出activityCompanyList中的有重复的CompanyName及mobilePhone
        List<String> duplicateCompanyNames = findDuplicateCompanyNames(activityCompanyList);
        List<String> duplicateMobilePhones = findDuplicateMobilePhones(activityCompanyList);
        String errorMsg = "";
        if (!existCompanyName.isEmpty()) {
            errorMsg += "以下单位名称已存在：" + StringUtils.join(existCompanyName, ",");
            errorMsg += "。";
        }
        if (!existMobilePhone.isEmpty()) {
            errorMsg += "以下手机号码已存在：" + StringUtils.join(existMobilePhone, ",");
            errorMsg += "。";
        }
        if (!duplicateCompanyNames.isEmpty()) {
            errorMsg += "表格中以下单位名称有重复：" + StringUtils.join(duplicateCompanyNames, ",");
            errorMsg += "。";
        }
        if (!duplicateMobilePhones.isEmpty()) {
            errorMsg += "表格中以下手机号码有重复：" + StringUtils.join(duplicateMobilePhones, ",");
            errorMsg += "。";
        }

        if (!StringUtils.isEmpty(errorMsg)) {
            return error(errorMsg);
        }
        activityCompanyService.saveBatch(activityCompanyList);
        return success("导入成功！");
    }

    /**
     * 删除单位信息
     */
    @PreAuthorize("@ss.hasPermi('ei:activityCompany:remove')")
    @Log(title = "删除单位信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @Operation(summary = "删除单位信息")
    public AjaxResult remove(@PathVariable String id) {
        ActivityCompany originalCompany = activityCompanyService.getById(id);
        if (originalCompany == null || originalCompany.getDeleteDt() != null) {
            return error("未找到单位信息");
        }
        //检查公司是否在邀约库里
        LambdaQueryWrapper<ActivityInvitation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityInvitation::getActivityCompanyId, id)
                .isNull(ActivityInvitation::getDeleteDt);
        if (activityInvitationService.count(wrapper) > 0) {
            return error("该单位存在邀约库记录，不允许删除！");
        }
        ActivityCompany activityCompany = new ActivityCompany();
        activityCompany.setId(id);
        activityCompany.setDeletedBy(getUserId());
        activityCompany.setDeleteDt(DateUtils.getNowDate());
        return toAjax(activityCompanyService.updateById(activityCompany));
    }

    @Log(title = "导出单位信息", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:activityCompany:export')")
    @PostMapping("/export")
    @Operation(summary = "导出单位信息")
    public void export(HttpServletResponse response, ActivityCompanyParams activityCompany) {
        List<ActivityCompanyVO> list = activityCompanyService.selectActivityList(activityCompany);
        ExcelUtil<ActivityCompanyVO> util = new ExcelUtil<>(ActivityCompanyVO.class);
        util.exportExcel(response, list, "单位信息");
    }

    private List<String> findDuplicateCompanyNames(List<ActivityCompany> activityCompanyList) {
        // 按 companyName 分组，统计出现次数 > 1 的 companyName
        return activityCompanyList.stream()
                .collect(Collectors.groupingBy(
                        ActivityCompany::getCompanyName,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 只保留重复的 companyName
                .map(Map.Entry::getKey)                 // 提取 companyName
                .collect(Collectors.toList());
    }

    private List<String> findDuplicateMobilePhones(List<ActivityCompany> activityCompanyList) {
        // 按 mobilePhone 分组，统计出现次数 > 1 的 mobilePhone
        return activityCompanyList.stream()
                .collect(Collectors.groupingBy(
                        ActivityCompany::getMobilePhone,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 只保留重复的 mobilePhone
                .map(Map.Entry::getKey)                 // 提取 mobilePhone
                .collect(Collectors.toList());
    }

}
