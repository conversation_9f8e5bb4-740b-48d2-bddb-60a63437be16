package com.szhr.ei.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.ei.converter.request.ActivityQueryParams;
import com.szhr.ei.converter.response.ActivityQueryResponseFromUser;
import com.szhr.ei.converter.response.HotelVO;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.ActivityAttachment;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.service.IActivityAttachmentService;
import com.szhr.ei.service.IActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 用户活动管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/front/ei/userActivity")
@Tag(name = "用户活动管理")
public class UserActivityController extends FrontBaseController{

    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityAttachmentService activityAttachmentService;

    /**
     * 查询用户参与的默认活动
     */
    @GetMapping("/default")
    @Operation(summary = "查询用户参与的默认活动")
    public AjaxResult getDefault(ActivityQueryParams activityQueryParams){
        activityQueryParams.setFairUserId(getFairUserId());
        activityQueryParams.setActivityId(getActivityId());
        List<ActivityQueryResponseFromUser> list = activityService.seclectActivityBasedOnFairUser(activityQueryParams);
        logger.info("list:{}", JSON.toJSONString(list));
        if (CollUtil.isNotEmpty(list)) {
            return success(list.get(0));
        }
        return error("活动不存在");
    }

    /**
     * 查询用户参与活动
     */
    @GetMapping("/list")
    @Operation(summary = "查询用户参与活动")
    public TableDataInfo list(ActivityQueryParams activityQueryParams){
        startPage();
        activityQueryParams.setFairUserId(getFairUserId());
        List<ActivityQueryResponseFromUser> list = activityService.seclectActivityBasedOnFairUser(activityQueryParams);
        return getDataTable(list);
    }

    /**
     * 获取招展行程活动
     */
    @GetMapping("/attach/{id}")
    public AjaxResult get(@PathVariable("id") String id) {
        ActivityAttachment attach = activityAttachmentService.getById(id);
        return AjaxResult.success(attach);
    }
}
