package com.szhr.ei.controller.front;

import com.szhr.common.core.page.TableDataInfo;
import com.szhr.ei.converter.request.ActivityQueryParams;
import com.szhr.ei.converter.response.ActivityQueryResponseFromUser;
import com.szhr.ei.service.IActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户活动管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/front/ei/userActivity")
@Tag(name = "用户活动管理")
public class UserActivityController extends FrontBaseController{

    @Resource
    private IActivityService activityService;

    /**
     * 查询用户参与活动
     */
    @GetMapping("/list")
    @Operation(summary = "查询用户参与活动")
    public TableDataInfo list(ActivityQueryParams activityQueryParams){
        startPage();
        String fairUserId = getFairUserId();
        activityQueryParams.setFairUserId(fairUserId);

        List<ActivityQueryResponseFromUser> list = activityService.seclectActivityBasedOnFairUser(activityQueryParams);
        return getDataTable(list);
    }
}
