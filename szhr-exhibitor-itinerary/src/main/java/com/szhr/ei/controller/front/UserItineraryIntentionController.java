package com.szhr.ei.controller.front;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.ItineraryIntentionConvert;
import com.szhr.ei.converter.request.ItineraryIntentionModelParams;
import com.szhr.ei.converter.request.ItineraryIntentionQueryParams;
import com.szhr.ei.converter.response.ItineraryIntentionVO;
import com.szhr.ei.domain.ItineraryIntention;
import com.szhr.ei.domain.ItineraryTicket;
import com.szhr.ei.service.IFairUserService;
import com.szhr.ei.service.IItineraryIntentionService;
import com.szhr.ei.service.IItineraryTicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import org.springframework.core.io.buffer.DataBuffer;
import reactor.core.publisher.Flux;


/**
 * 登记行程飞机高铁意向管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/itineraryIntention")
@Tag(name = "登记行程飞机高铁意向管理")
public class UserItineraryIntentionController extends FrontBaseController {

    @Resource
    private IItineraryIntentionService itineraryIntentionService;

    @Resource
    private IItineraryTicketService itineraryTicketService;

    @Resource
    private IFairUserService fairUserService;

    @Resource
    private ItineraryIntentionConvert itineraryIntentionConvert;

    /**
     * 新增登记行程飞机高铁意向
     */
    @PostMapping("/add")
    @Operation(summary = "新增登记行程飞机高铁意向")
    @RepeatSubmit
    public AjaxResult add(ServerHttpRequest serverHttpRequest, @Validated @RequestBody ItineraryIntentionModelParams itineraryIntentionModelParams) {
        Flux<DataBuffer> body = serverHttpRequest.getBody();

        LambdaQueryWrapper<ItineraryTicket> ticketWrapper = new LambdaQueryWrapper<>();
        ticketWrapper.eq(ItineraryTicket::getActivityId, itineraryIntentionModelParams.getActivityId());
        ticketWrapper.eq(ItineraryTicket::getFairUserId, itineraryIntentionModelParams.getFairUserId());
        ticketWrapper.eq(ItineraryTicket::getTicketDirection, itineraryIntentionModelParams.getTicketDirection());
        if (itineraryTicketService.exists(ticketWrapper)) {
            return error("该出行飞机或高铁班次记录已存在");
        }

        LambdaQueryWrapper<ItineraryIntention> intentionWrapper = new LambdaQueryWrapper<>();
        intentionWrapper.eq(ItineraryIntention::getActivityId, itineraryIntentionModelParams.getActivityId());
        intentionWrapper.eq(ItineraryIntention::getFairUserId, itineraryIntentionModelParams.getFairUserId());
        intentionWrapper.eq(ItineraryIntention::getTicketDirection, itineraryIntentionModelParams.getTicketDirection());
        if (itineraryIntentionService.exists(intentionWrapper)) {
            return error("该登记行程飞机高铁意向记录已存在");
        }

        ItineraryIntention itineraryIntention = itineraryIntentionConvert.toPo(itineraryIntentionModelParams);
        itineraryIntention.setCreateDt(DateUtils.getNowDate());
        itineraryIntention.setItineraryIntentionId(IdUtils.randomUUID());
        if (itineraryIntention.getStatus() == null) {
            itineraryIntention.setStatus(DictOptionConstans.COMMON_STATUS_INIT);
        }
        itineraryIntention.setActivityCompanyId(getFairUser().getActivityCompanyId());
        return toAjax(itineraryIntentionService.save(itineraryIntention));
    }

    /**
     * 更新登记行程飞机高铁意向
     */
    @PostMapping("/update")
    @Operation(summary = "更新登记行程飞机高铁意向")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody ItineraryIntention itineraryIntention) {
        if (StringUtils.isBlank(itineraryIntention.getItineraryIntentionId())) {
            return error("参数错误");
        }
        ItineraryIntention queryItineraryIntention = itineraryIntentionService.getById(itineraryIntention.getItineraryIntentionId());
        if (queryItineraryIntention == null) {
            return error("登记行程飞机高铁意向不存在");
        }

        return toAjax(itineraryIntentionService.updateById(itineraryIntention));
    }

    /**
     * 删除登记行程飞机高铁意向
     */
    @DeleteMapping("/{itineraryIntentionId}")
    @Operation(summary = "删除登记行程飞机高铁意向")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "itineraryIntentionId", required = true) String itineraryIntentionId) {
        if (StringUtils.isBlank(itineraryIntentionId)) {
            return error("参数错误");
        }
        ItineraryIntention queryItineraryIntention = itineraryIntentionService.getById(itineraryIntentionId);
        if (queryItineraryIntention == null) {
            return error("登记行程飞机高铁意向不存在");
        }

        return toAjax(itineraryIntentionService.removeById(itineraryIntentionId));
    }

    /**
     * 查询登记行程飞机高铁意向列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询登记行程飞机高铁意向列表")
    public TableDataInfo list(ItineraryIntentionQueryParams itineraryIntentionQueryParams) {
        startPage();
        itineraryIntentionQueryParams.setFairUserId(getFairUserId());
        List<ItineraryIntentionVO> list = itineraryIntentionService.selectItineraryIntentionVOList(itineraryIntentionQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看登记行程飞机高铁意向详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查看登记行程飞机高铁意向详情")
    public AjaxResult get(@PathVariable("id") String id) {
        return AjaxResult.success(itineraryIntentionService.getById(id));
    }
    

}
