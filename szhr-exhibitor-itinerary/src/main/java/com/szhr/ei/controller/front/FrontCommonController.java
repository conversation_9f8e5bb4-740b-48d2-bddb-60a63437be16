package com.szhr.ei.controller.front;

import com.szhr.common.config.SzhrConfig;
import com.szhr.common.constant.Constants;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.utils.AESUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.FileUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.framework.config.ServerConfig;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 前端用户通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/front/ei/common")
public class FrontCommonController {
    private static final Logger log = LoggerFactory.getLogger(FrontCommonController.class);

    @GetMapping("/imageStream/{encodeFileName}")
    @Operation(summary = "查看图片")
    @ResponseBody
    public void imageStream(@PathVariable String encodeFileName, HttpServletResponse response) throws Exception {
        String objectName = AESUtils.decrypt(encodeFileName);
        if(StringUtils.isNotBlank(objectName)) {
            try (InputStream inputStream = MinioUtil.getObject(objectName)) {
                // 2. 设置响应头（inline预览 + 编码处理）
                // 检测MIME类型
                response.setContentType(MinioUtil.getContentTypeByObjectName(objectName));

                String fileName = FileUtils.getName(objectName);
                fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20"); // 处理空格编码[6,7](@ref)
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"");

                // 3. 流复制（使用Apache Commons IOUtils简化操作）
                IOUtils.copy(inputStream, response.getOutputStream());
            } catch (Exception e) {
                log.error("文件下载失败", e);
                throw new RuntimeException("文件下载失败");
            }
        }
    }

    @Operation(summary = "下载附件")
    @GetMapping("/download/{encodeFileName}")
    @ResponseBody
    public void download(@PathVariable String encodeFileName, HttpServletResponse response) throws Exception {
        String objectName = AESUtils.decrypt(encodeFileName);
        String fileName = FileUtils.getName(objectName);
        if(StringUtils.isNotBlank(objectName)) {
            try (InputStream inputStream = MinioUtil.getObject(objectName)) {
                // 设置响应头
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
                // 流式传输文件
                IOUtils.copy(inputStream, response.getOutputStream());
                response.flushBuffer();
            } catch (Exception e) {
                log.error("文件下载失败", e);
                throw new RuntimeException("文件下载失败");
            }
        }
    }

}
