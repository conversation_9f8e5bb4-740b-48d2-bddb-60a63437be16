package com.szhr.ei.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.response.HotelRoomAllocationResponce;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.service.IHotelRoomAllocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房间分配记录管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/hotelRoomAllocation")
@Tag(name = "房间分配记录管理")
public class UserHotelRoomAllocationController extends FrontBaseController {

    @Resource
    private IHotelRoomAllocationService hotelRoomAllocationService;

    /**
     * 新增房间分配记录
     */
    @PostMapping("/add")
    @Operation(summary = "新增房间分配记录")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody HotelRoomAllocation hotelRoomAllocation) {
        List<HotelRoomAllocation> list = hotelRoomAllocationService.listByCondition(hotelRoomAllocation);
        if (CollUtil.isNotEmpty(list)) {
            return error("该房间分配记录已存在");
        }

        hotelRoomAllocation.setHotelRoomAllocationId(IdUtils.randomUUID());
        hotelRoomAllocation.setCreateDt(DateUtils.getNowDate());
        return toAjax(hotelRoomAllocationService.save(hotelRoomAllocation));
    }

    /**
     * 更新房间分配记录
     */
    @PostMapping("/update")
    @Operation(summary = "更新房间分配记录")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody HotelRoomAllocation hotelRoomAllocation) {
        if (StringUtils.isBlank(hotelRoomAllocation.getHotelRoomAllocationId())) {
            return error("参数错误");
        }
        HotelRoomAllocation queryHotelRoomAllocation = hotelRoomAllocationService.getById(hotelRoomAllocation.getHotelRoomAllocationId());
        if (queryHotelRoomAllocation == null) {
            return error("房间分配记录不存在");
        }

        return toAjax(hotelRoomAllocationService.updateById(hotelRoomAllocation));
    }

    /**
     * 查询房间分配记录列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询房间分配记录列表")
    public TableDataInfo list(String activityId) {
        startPage();
        HotelRoomAllocation hotelRoomAllocation = new HotelRoomAllocation();
        if (StringUtils.isNotBlank(activityId)) {
            hotelRoomAllocation.setActivityId(activityId);
        }
        hotelRoomAllocation.setFairUserId(getFairUserId());
        List<HotelRoomAllocationResponce> list = hotelRoomAllocationService.selectHotelInformationList(hotelRoomAllocation);
        return getDataTable(list);
    }

    /**
     * 查看房间分配记录详情
     */
    @GetMapping("/{hotelRoomAllocationId}")
    @Operation(summary = "查看房间分配记录详情")
    public AjaxResult get(@PathVariable("hotelRoomAllocationId") String hotelRoomAllocationId) {
        return AjaxResult.success(hotelRoomAllocationService.getById(hotelRoomAllocationId));
    }



}
