package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.domain.PickupSchedule;
import com.szhr.ei.service.IPickupScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 接送安排管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/pickupSchedule")
@Tag(name = "接送安排管理")
public class PickupScheduleController extends BaseController {

    @Resource
    private IPickupScheduleService pickupScheduleService;

    /**
     * 新增接送安排
     */
    @PreAuthorize("@ss.hasPermi('ei:pickupSchedule:add')")
    @Log(title = "新增接送安排", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增接送安排")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody PickupSchedule pickupSchedule) {
        List<PickupSchedule> list = pickupScheduleService.listByCondition(pickupSchedule);
        if (CollUtil.isNotEmpty(list)) {
            return error("该接送安排已存在");
        }

        pickupSchedule.setId(IdUtils.randomUUID());
        logger.debug("pickupSchedule: {} ", JSON.toJSONString(pickupSchedule));
        return toAjax(pickupScheduleService.save(pickupSchedule));
    }

    /**
     * 更新接送安排
     */
    @PreAuthorize("@ss.hasPermi('ei:pickupSchedule:update')")
    @Log(title = "更新接送安排", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新接送安排")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody PickupSchedule pickupSchedule) {
        if (StringUtils.isBlank(pickupSchedule.getId())) {
            return error("参数错误");
        }
        PickupSchedule queryPickupSchedule = pickupScheduleService.getById(pickupSchedule.getId());
        if (queryPickupSchedule == null) {
            return error("接送安排不存在");
        }

        return toAjax(pickupScheduleService.updateById(pickupSchedule));
    }

    /**
     * 删除接送安排
     */
    @PreAuthorize("@ss.hasPermi('ei:pickupSchedule:delete')")
    @Log(title = "删除接送安排", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除接送安排")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable("id") String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        PickupSchedule queryPickupSchedule = pickupScheduleService.getById(id);
        if (queryPickupSchedule == null) {
            return error("接送安排不存在");
        }

        return toAjax(pickupScheduleService.removeById(id));
    }

    /**
     * 查询接送安排列表
     */
    @PreAuthorize("@ss.hasPermi('ei:pickupSchedule:list')")
    @GetMapping("/list")
    @Operation(summary = "查询接送安排列表")
    public TableDataInfo list(PickupSchedule pickupSchedule) {
        startPage();
        List<PickupSchedule> list = pickupScheduleService.listByCondition(pickupSchedule);
        return getDataTable(list);
    }

    /**
     * 查看接送安排详情
     */
    @PreAuthorize("@ss.hasPermi('ei:pickupSchedule:get')")
    @GetMapping("/{id}")
    @Operation(summary = "查看接送安排详情")
    public AjaxResult get(@PathVariable("id") String id) {
        return AjaxResult.success(pickupScheduleService.getById(id));
    }


}
