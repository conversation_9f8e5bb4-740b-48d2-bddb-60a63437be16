package com.szhr.ei.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.bean.BeanUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.ActivityInvitationEditParams;
import com.szhr.ei.converter.request.ActivityInvitationParams;
import com.szhr.ei.converter.request.ActivityInvitationRequestParams;
import com.szhr.ei.converter.request.ActivityInvitationStateParams;
import com.szhr.ei.converter.response.ActivityInvitationVO;
import com.szhr.ei.domain.ActivityCompany;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.service.IActivityCompanyService;
import com.szhr.ei.service.IActivityInvitationService;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IFairUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 招展行程活动单位信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/activityInvitation")
@Tag(name = "邀约库管理")
public class ActivityInvitationController extends BaseController {

    @Resource
    private IActivityCompanyService activityCompanyService;

    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityInvitationService   activityInvitationService;

    @Resource
    private IFairUserService fairUserService;

    /**
     * 获取邀约库列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:list')")
    @GetMapping("/list")
    @Operation(summary = "查询邀约库列表")
    public TableDataInfo list(ActivityInvitationParams activityInvitation) {
        startPage();
        return getDataTable(activityInvitationService.selectActivityInvitationList(activityInvitation));
    }

    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:add')")
    @Log(title = "新增邀约库单位信息", businessType = BusinessType.INSERT)
    @PostMapping("/addByActivityCompanyIds")
    @Operation(summary = "新增邀约库单位信息")
    public AjaxResult addByActivityCompanyIds(@RequestBody ActivityInvitationRequestParams activityInvitationRequestParams) {
        if (activityInvitationRequestParams == null) {
            return error("参数错误！");
        }
        if (activityInvitationRequestParams.getActivityCompanyIds().isEmpty()) {
            return error("请选择单位信息");
        }
        if (activityInvitationRequestParams.getActivityId() == null) {
            return error("活动id不能为空！");
        }

        List<ActivityCompany> activityCompanyList =  activityCompanyService.listByIds(activityInvitationRequestParams.getActivityCompanyIds());
        List<ActivityInvitation> needAddActivityInvitationList = new ArrayList<>();
        List<String> existCompanyName = new ArrayList<>();
        Date date = DateUtils.getNowDate();
        for (ActivityCompany activityCompany : activityCompanyList) {
            ActivityInvitation activityInvitation = new ActivityInvitation();
            activityInvitation.setId(IdUtils.randomUUID());
            activityInvitation.setActivityId(activityInvitationRequestParams.getActivityId());
            activityInvitation.setActivityCompanyId(activityCompany.getId());
            activityInvitation.setCreateDt(date);
            activityInvitation.setCreatedBy(getUserId());
            activityInvitation.setState(0);
            activityInvitation.setManagedBy(getUserId());
            activityInvitation.setLastUpdatedBy(getUserId());
            activityInvitation.setLastUpdateDt(DateUtils.getNowDate());
            needAddActivityInvitationList.add(activityInvitation);
            //检查公司是否已经存在邀约库
            LambdaQueryWrapper<ActivityInvitation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityInvitation::getActivityCompanyId, activityCompany.getId())
                    .eq(ActivityInvitation::getActivityId, activityInvitationRequestParams.getActivityId())
                    .isNull(ActivityInvitation::getDeleteDt);
            if (activityInvitationService.count(wrapper) > 0) {
                existCompanyName.add(activityCompany.getCompanyName());
            }
        }
        String errorMsg = "";
        if (!existCompanyName.isEmpty()) {
            errorMsg += "以下单位名称已添加过邀约库：" + StringUtils.join(existCompanyName, ",");
            errorMsg += "。";
        }
        if (!StringUtils.isEmpty(errorMsg)) {
            return error(errorMsg);
        }
        if(needAddActivityInvitationList.isEmpty()) {
            return success("无新增数据！");
        }

        return toAjax(activityInvitationService.saveBatch(needAddActivityInvitationList));
    }

    @Operation(summary = "下载邀约库单位信息导入模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String[] fields = {"companyName"};
        ExcelUtil<ActivityCompany> util = new ExcelUtil<>(ActivityCompany.class);
        util.showColumn(fields);
        util.importTemplateExcel(response, "邀约库单位信息导入");
    }

    @Log(title = "邀约库单位信息导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:import')")
    @PostMapping("/importData")
    @Operation(summary = "邀约库单位信息导入")
    public AjaxResult importData(MultipartFile file, String activityId) throws Exception {
        if (activityService.getById(activityId) == null) {
            return error("活动不存在！");
        }
        ExcelUtil<ActivityCompany> util = new ExcelUtil<ActivityCompany>(ActivityCompany.class);
        List<ActivityCompany> activityCompanyList = util.importExcel(file.getInputStream());
        if (activityCompanyList == null || activityCompanyList.isEmpty()) {
            return error("导入数据不能为空！");
        }
        Date date = DateUtils.getNowDate();
        //检查公司名称是否已经存在邀约库
        List<String> existInvitationCompanyNameList = new ArrayList<>();
        //检查公司名称是否未存在单位库里
        List<String> notExistCompanyNameList = new ArrayList<>();
        List<ActivityInvitation> needAddActivityInvitationList = new ArrayList<>();
        for (ActivityCompany activityCompany : activityCompanyList) {
            //添加到邀约库列表
            ActivityInvitation activityInvitation = new ActivityInvitation();
            LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getCompanyName, activityCompany.getCompanyName())
                    .isNull(ActivityCompany::getDeleteDt);

            ActivityCompany existActivityCompany = activityCompanyService.getOne( wrapper);
            if (existActivityCompany == null) {
                notExistCompanyNameList.add(activityCompany.getCompanyName());
            } else {
                activityInvitation.setActivityCompanyId(existActivityCompany.getId());
                //检查公司是否已经存在邀约库
                LambdaQueryWrapper<ActivityInvitation> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(ActivityInvitation::getActivityCompanyId, existActivityCompany.getId())
                        .eq(ActivityInvitation::getActivityId, activityId)
                        .isNull(ActivityInvitation::getDeleteDt);
                if (activityInvitationService.count(wrapper2) > 0) {
                    existInvitationCompanyNameList.add(activityCompany.getCompanyName());
                }
            }
            activityInvitation.setId(IdUtils.randomUUID());
            activityInvitation.setActivityId(activityId);
            activityInvitation.setCreateDt(date);
            activityInvitation.setCreatedBy(getUserId());
            activityInvitation.setState(0);
            activityInvitation.setManagedBy(getUserId());
            activityInvitation.setLastUpdatedBy(getUserId());
            activityInvitation.setLastUpdateDt(DateUtils.getNowDate());
            needAddActivityInvitationList.add(activityInvitation);
        }
        //取出activityCompanyList中的有重复的CompanyName
        List<String> duplicateCompanyNames = findDuplicateCompanyNames(activityCompanyList);
        String errorMsg = "";
        if (!notExistCompanyNameList.isEmpty()) {
            errorMsg += "以下单位名称未存在单位基础信息，请从单位信息管理添加单位信息：" + StringUtils.join(notExistCompanyNameList, ",");
            errorMsg += "。";
        }
        if (!existInvitationCompanyNameList.isEmpty()) {
            errorMsg += "以下单位名称已添加过邀约库：" + StringUtils.join(existInvitationCompanyNameList, ",");
            errorMsg += "。";
        }
        if (!duplicateCompanyNames.isEmpty()) {
            errorMsg += "以下单位名称在表格中有重复：" + StringUtils.join(duplicateCompanyNames, ",");
            errorMsg += "。";
        }
        if (!StringUtils.isEmpty(errorMsg)) {
            return error(errorMsg);
        }
        if(needAddActivityInvitationList.isEmpty()) {
            return success("无新增邀约库数据！");
        }
        activityInvitationService.saveBatch(needAddActivityInvitationList);
        return success("导入成功！");
    }

    /**
     * 修改招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:edit')")
    @Log(title = "编辑邀约信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @Operation(summary = "编辑邀约信息")
    public AjaxResult edit(@RequestBody ActivityInvitationEditParams activityInvitationEditParams) {
        ActivityInvitation activityInvitation = new ActivityInvitation();
        BeanUtils.copyBeanProp(activityInvitation, activityInvitationEditParams);
        activityInvitation.setManagedBy(getUserId());
        activityInvitation.setLastUpdatedBy(getUserId());
        activityInvitation.setLastUpdateDt(DateUtils.getNowDate());
        return toAjax(activityInvitationService.updateById(activityInvitation));
    }

    /**
     * 修改招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:editState')")
    @Log(title = "编辑邀约状态", businessType = BusinessType.UPDATE)
    @PostMapping("/editState")
    @Operation(summary = "编辑邀约状态")
    public AjaxResult editState(@RequestBody ActivityInvitationStateParams ActivityInvitationStateParams) {
        ActivityInvitation queryActivityInvitation = activityInvitationService.getById(ActivityInvitationStateParams.getId());
        if (queryActivityInvitation == null || queryActivityInvitation.getDeleteDt() != null) {
            return error("未找到邀约记录");
        }
//        if (queryActivityInvitation.getState() == 2) {
//            return error("该邀约已入库，不允许修改！");
//        }
//        if (ActivityInvitationStateParams.getState() != 2) {
//            return error("非法的状态参数！");
//        }
        ActivityInvitation activityInvitation = new ActivityInvitation();
        BeanUtils.copyBeanProp(activityInvitation, ActivityInvitationStateParams);
        activityInvitation.setManagedBy(getUserId());
        activityInvitation.setLastUpdatedBy(getUserId());
        activityInvitation.setLastUpdateDt(DateUtils.getNowDate());
        return toAjax(activityInvitationService.updateById(activityInvitation));
    }

    /**
     * 删除单位信息
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:remove')")
    @Log(title = "删除邀约信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable String id) {
        ActivityInvitation queryActivityInvitation = activityInvitationService.getById(id);
        if (queryActivityInvitation == null || queryActivityInvitation.getDeleteDt() != null) {
            return error("未找到邀约记录");
        }
        //检查公司是否在行程人员信息
        LambdaQueryWrapper<FairUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairUser::getActivityCompanyId, queryActivityInvitation.getActivityCompanyId())
                .eq(FairUser::getActivityId, queryActivityInvitation.getActivityId())
                .isNull(FairUser::getDeleteDt);
        if (fairUserService.count(wrapper) > 0) {
            return error("该单位存在行程人员，不允许删除！");
        }
        ActivityInvitation activityInvitation = new ActivityInvitation();
        activityInvitation.setId(id);
        activityInvitation.setDeletedBy(getUserId());
        activityInvitation.setDeleteDt(DateUtils.getNowDate());
        return toAjax(activityInvitationService.updateById(activityInvitation));
    }

    private List<String> findDuplicateCompanyNames(List<ActivityCompany> activityCompanyList) {
        // 按 companyName 分组，统计出现次数 > 1 的 companyName
        return activityCompanyList.stream()
                .collect(Collectors.groupingBy(
                        ActivityCompany::getCompanyName,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 只保留重复的 companyName
                .map(Map.Entry::getKey)                 // 提取 companyName
                .collect(Collectors.toList());
    }

    /**
     * 获取邀约单位列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:listCompany')")
    @GetMapping("/listCompany")
    @Operation(summary = "获取邀约单位列表")
    public TableDataInfo listCompany(ActivityInvitationParams activityInvitation) {
        startPage();
        return getDataTable(activityInvitationService.listCompany(activityInvitation));
    }

    /**
     * 获取邀约单位列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:listOptionalCompany')")
    @GetMapping("/listOptionalCompany")
    @Operation(summary = "获取可选择的邀约单位列表")
    public TableDataInfo listOptionalCompany(ActivityInvitationParams activityInvitation) {
        startPage();
        return getDataTable(activityInvitationService.listOptionalCompany(activityInvitation));
    }

    @Log(title = "导出邀约库信息", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:activityInvitation:export')")
    @PostMapping("/export")
    @Operation(summary = "导出邀约库信息")
    public void export(HttpServletResponse response, ActivityInvitationParams activityInvitation) {
        List<ActivityInvitationVO> list = new ArrayList<>();
        if(StringUtils.isNotBlank(activityInvitation.getActivityId())) {
            list = activityInvitationService.selectActivityInvitationList(activityInvitation);
        }
        ExcelUtil<ActivityInvitationVO> util = new ExcelUtil<>(ActivityInvitationVO.class);
        util.exportExcel(response, list, "邀约库信息");
    }
}
