package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.MealReservationConvert;
import com.szhr.ei.converter.request.MealReservationQueryParams;
import com.szhr.ei.converter.response.MealReservationExport;
import com.szhr.ei.converter.response.MealReservationVO;
import com.szhr.ei.domain.MealReservation;
import com.szhr.ei.service.IMealReservationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户订餐信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/mealReservation")
@Tag(name = "用户订餐信息管理")
public class MealReservationController extends BaseController {

    @Resource
    private IMealReservationService mealReservationService;

    @Resource
    private MealReservationConvert mealReservationConvert;

    /**
     * 新增用户订餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:add')")
    @Log(title = "新增用户订餐信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增用户订餐信息")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody MealReservation mealReservation) {
        List<MealReservation> list = mealReservationService.listByCondition(mealReservation);
        if (CollUtil.isNotEmpty(list)) {
            return error("该用户订餐信息已存在");
        }

        mealReservation.setMealReservationId(IdUtils.randomUUID());
        return toAjax(mealReservationService.save(mealReservation));
    }

    /**
     * 更新用户订餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:update')")
    @Log(title = "更新用户订餐信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新用户订餐信息")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody MealReservation mealReservation) {
        if (StringUtils.isBlank(mealReservation.getMealReservationId())) {
            return error("参数错误");
        }
        MealReservation queryMealReservation = mealReservationService.getById(mealReservation.getMealReservationId());
        if (queryMealReservation == null) {
            return error("用户订餐信息不存在");
        }

        return toAjax(mealReservationService.updateById(mealReservation));
    }

    /**
     * 删除用户订餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:delete')")
    @Log(title = "删除用户订餐信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mealReservationId}")
    @Operation(summary = "删除用户订餐信息")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "mealReservationId", required = true) String mealReservationId) {
        if (StringUtils.isBlank(mealReservationId)) {
            return error("参数错误");
        }
        MealReservation queryMealReservation = mealReservationService.getById(mealReservationId);
        if (queryMealReservation == null) {
            return error("用户订餐信息不存在");
        }

        return toAjax(mealReservationService.removeById(mealReservationId));
    }

    /**
     * 查询用户订餐信息列表
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:list')")
    @GetMapping("/list")
    @Operation(summary = "查询用户订餐信息列表")
    public TableDataInfo list(MealReservationQueryParams mealReservationQueryParams) {
        startPage();
        List<MealReservationVO> list = mealReservationService.selectMealReservationVOList(mealReservationQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看用户订餐信息详情
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:get')")
    @GetMapping("/{mealReservationId}")
    @Operation(summary = "查看用户订餐信息详情")
    public AjaxResult get(@PathVariable("mealReservationId") String mealReservationId) {
        MealReservationQueryParams queryParams = new MealReservationQueryParams();
        queryParams.setMealReservationId(mealReservationId);
        List<MealReservationVO> list = mealReservationService.selectMealReservationVOList(queryParams);
        MealReservationVO mealReservationVO = new MealReservationVO();
        if (CollUtil.isNotEmpty(list)) {
            mealReservationVO = list.get(0);
        }
        return success(mealReservationVO);
    }

    @Log(title = "导出用户订餐信息", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, MealReservationQueryParams mealReservationQueryParams) {
        List<MealReservationVO> list = mealReservationService.selectMealReservationVOList(mealReservationQueryParams);
        logger.debug("export MealReservationVO: {}", JSON.toJSONString(list.get(0)));
        List<MealReservationExport> exportList = list.stream().map(item -> mealReservationConvert.toExport(item)).collect(Collectors.toList());
        logger.debug("export MealReservationExport: {}", JSON.toJSONString(exportList.get(0)));
        ExcelUtil<MealReservationExport> util = new ExcelUtil<MealReservationExport>(MealReservationExport.class);
        util.exportExcel(response, exportList, "导出用户订餐信息");
    }

    /**
     * 统计用户订餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealReservation:stat')")
    @GetMapping("/stat")
    @Operation(summary = "统计用户订餐信息")
    public AjaxResult stat(MealReservation mealReservation) {
        logger.debug("stat MealReservation: {}", JSON.toJSONString(mealReservation));
        List<MealReservation> list = mealReservationService.listByCondition(mealReservation);
        Map<String, Integer> statMap = new HashMap<>();
        if (CollUtil.isEmpty(list)) {
            statMap.put("cntLunch", 0);
            statMap.put("dining", 0);
            return success(statMap);
        }
        List<MealReservation> lunchList = list.stream().filter(item -> item.getMealType() == DictOptionConstans.MEAL_TYPE_LUNCHBOX).collect(Collectors.toList());
        List<MealReservation> diningList = list.stream().filter(item -> item.getMealType() == DictOptionConstans.MEAL_TYPE_COMMUNAL_DINING).collect(Collectors.toList());
        statMap.put("cntLunch", Optional.of(lunchList).map(List::size).orElse(0));
        statMap.put("cntDining", Optional.of(diningList).map(List::size).orElse(0));
        return success(statMap);
    }


}
