package com.szhr.ei.controller;

import com.szhr.common.annotation.Log;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.domain.ActivityAttachment;
import com.szhr.ei.service.IActivityAttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 招展行程活动附件Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/activityAttachment")
@Tag(name = "活动附件")
public class ActivityAttachmentController extends BaseController {

    @Resource
    private IActivityAttachmentService activityAttachmentService;

    /**
     * 获取活动附件列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activityAttachment:list')")
    @GetMapping("/list")
    @Operation(summary = "通过活动ID查询附件列表")
    public TableDataInfo list(ActivityAttachment activityAttachment) {
        return getDataTable(activityAttachmentService.selectActivityAttachmentList(activityAttachment));
    }

    /**
     * 新增招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activityAttachment:add')")
    @Log(title = "活动附件上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    @Operation(summary = "新增活动附件")
    public AjaxResult upload(@Validated ActivityAttachment activityAttachment,
                          @RequestParam(value = "file", required = true) MultipartFile file) {
        activityAttachment.setId(IdUtils.randomUUID());
        activityAttachment.setCreateDt(DateUtils.getNowDate());
        activityAttachment.setCreatedBy(getUserId());
        try {
            String objectName = FileUploadUtils.uploadMinio(FilePrefixConstants.ACTIVITY, file);
            activityAttachment.setLinkUrl(objectName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return toAjax(activityAttachmentService.save(activityAttachment));
    }

    /**
     * 删除招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activityAttachment:remove')")
    @Log(title = "活动附件删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable List<String> ids) {
        List<String> linkUrlList =  activityAttachmentService.getLinkUrlListByIds(ids);
        boolean resultStatus = activityAttachmentService.removeBatchByIds(ids);
        if(resultStatus) {
            try {
                MinioUtil.removeObjects(linkUrlList);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return toAjax(resultStatus);
    }

}
