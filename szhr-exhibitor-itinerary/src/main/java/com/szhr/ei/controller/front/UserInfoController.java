package com.szhr.ei.controller.front;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.constant.DropdownDictConstants;
import com.szhr.ei.converter.mapstruct.FairUserConvert;
import com.szhr.ei.converter.request.FairUserParams;
import com.szhr.ei.converter.response.FairUserLoginVO;
import com.szhr.ei.converter.response.FairUserVO;
import com.szhr.ei.domain.ActivityCompany;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.service.IActivityCompanyService;
import com.szhr.ei.service.IActivityInvitationService;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IFairUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 参会单位个人用户基本信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/fairUser")
@Tag(name = "参会单位个人用户基本信息管理")
public class UserInfoController extends FrontBaseController {

    @Autowired
    private FairUserConvert fairUserConvert;

    @GetMapping("/basic")
    @Operation(summary = "登录后查看本人基本信息")
    public AjaxResult getBasic() {
        FairUser fairUser = getFairUser();
        FairUserLoginVO fairUserLoginVO = fairUserConvert.toVo(fairUser);
        return success(fairUserLoginVO);
    }

}
