package com.szhr.ei.controller.front;

import com.szhr.common.core.domain.AjaxResult;
import com.szhr.ei.converter.mapstruct.FairUserConvert;
import com.szhr.ei.converter.response.FairUserLoginVO;
import com.szhr.ei.domain.FairUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 参会单位个人用户基本信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/fairUser")
@Tag(name = "参会单位个人用户基本信息管理")
public class UserInfoController extends FrontBaseController {

    @Autowired
    private FairUserConvert fairUserConvert;

    @GetMapping("/basic")
    @Operation(summary = "登录后查看本人基本信息")
    public AjaxResult getBasic() {
        FairUser fairUser = getFairUser();
        FairUserLoginVO fairUserLoginVO = fairUserConvert.toVo(fairUser);
        return success(fairUserLoginVO);
    }

}
