package com.szhr.ei.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.MealOptionConvert;
import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.service.IMealOptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 前端餐饮套餐信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/mealOption")
@Tag(name = "餐饮套餐信息管理")
public class FrontMealOptionController extends BaseController {

    @Resource
    private IMealOptionService mealOptionService;

    @Resource
    private MealOptionConvert mealOptionConvert;

    /**
     * 查询餐饮套餐信息列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询餐饮套餐信息列表")
    public TableDataInfo list(MealOptionQueryParams mealOptionQueryParams) {
        startPage();
        List<MealOptionVO> list = mealOptionService.selectVOList(mealOptionQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看餐饮套餐信息详情
     */
    @GetMapping("/{mealOptionId}")
    @Operation(summary = "查看餐饮套餐信息详情")
    public AjaxResult get(@PathVariable("mealOptionId") String mealOptionId) {
        logger.debug("MealOption get {} ", mealOptionId);
        return AjaxResult.success(mealOptionService.getById(mealOptionId));
    }


}
