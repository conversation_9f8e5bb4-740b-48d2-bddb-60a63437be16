package com.szhr.ei.controller.front;

import com.alibaba.fastjson2.JSONObject;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.ei.service.IActivityInvitationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI批量外呼管理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/front/ei/userActivityInvitation")
@Tag(name = "AI批量外呼管理")
public class UserActivityInvitationController extends FrontBaseController {

    @Resource
    private IActivityInvitationService activityInvitationService;


    /**
     * 发起AI批量外呼
     */




}
