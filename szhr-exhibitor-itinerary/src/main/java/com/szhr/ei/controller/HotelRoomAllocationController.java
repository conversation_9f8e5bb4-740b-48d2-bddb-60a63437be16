package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.HotelRoomAllocationQueryParams;
import com.szhr.ei.converter.request.HotelRoomSmsMailParam;
import com.szhr.ei.converter.response.HotelRoomAllocationExport;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.service.IHotelRoomAllocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房间分配记录管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/hotelRoomAllocation")
@Tag(name = "房间分配记录管理")
public class HotelRoomAllocationController extends BaseController {

    @Resource
    private IHotelRoomAllocationService hotelRoomAllocationService;

    /**
     * 新增房间分配记录
     */
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:add')")
    @Log(title = "新增房间分配记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增房间分配记录")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody HotelRoomAllocation hotelRoomAllocation) {
        // 同一个活动、公司、用户不能重复
        List<HotelRoomAllocation> list = hotelRoomAllocationService.listByCondition(hotelRoomAllocation);
        if (CollUtil.isNotEmpty(list)) {
            return error("该用户分配记录已存在");
        }

        hotelRoomAllocation.setHotelRoomAllocationId(IdUtils.randomUUID());
        // 填写默认值
        if (hotelRoomAllocation.getGroupWithSameCompany() == null) {
            hotelRoomAllocation.setGroupWithSameCompany(1);
        }

        hotelRoomAllocation.setStatus(0);
        return toAjax(hotelRoomAllocationService.save(hotelRoomAllocation));
    }

    /**
     * 更新房间分配记录
     */
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:update')")
    @Log(title = "更新房间分配记录", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新房间分配记录")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody HotelRoomAllocation hotelRoomAllocation) {
        logger.debug("HotelRoomAllocation update:{}", JSON.toJSONString(hotelRoomAllocation));
        if (StringUtils.isBlank(hotelRoomAllocation.getHotelRoomAllocationId())) {
            return error("参数错误");
        }
        HotelRoomAllocation queryHotelRoomAllocation = hotelRoomAllocationService.getById(hotelRoomAllocation.getHotelRoomAllocationId());
        if (queryHotelRoomAllocation == null) {
            return error("房间分配记录不存在");
        }

        return toAjax(hotelRoomAllocationService.updateById(hotelRoomAllocation));
    }

    /**
     * 删除房间分配记录
     */
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:delete')")
    @Log(title = "删除房间分配记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除房间分配记录")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "id") String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        HotelRoomAllocation queryHotelRoomAllocation = hotelRoomAllocationService.getById(id);
        if (queryHotelRoomAllocation == null) {
            return error("房间分配记录不存在");
        }

        return toAjax(hotelRoomAllocationService.removeById(id));
    }

    /**
     * 查询房间分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:list')")
    @GetMapping("/list")
    @Operation(summary = "查询房间分配记录列表")
    public TableDataInfo list(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams) {
        startPage();
        List<HotelRoomAllocationExport> list = hotelRoomAllocationService.selectHotelRoomAllocationExportList(hotelRoomAllocationQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看房间分配记录详情
     */
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:get')")
    @GetMapping("/{hotelRoomAllocationId}")
    @Operation(summary = "查看房间分配记录详情")
    public AjaxResult get(@PathVariable("hotelRoomAllocationId") String hotelRoomAllocationId) {
        return AjaxResult.success(hotelRoomAllocationService.getById(hotelRoomAllocationId));
    }

    @Log(title = "导出房间分配记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:export')")
    @PostMapping("/export")
    @Operation(summary = "导出数据")
    public void export(HttpServletResponse response, HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams) {
        List<HotelRoomAllocationExport> list = hotelRoomAllocationService.selectHotelRoomAllocationExportList(hotelRoomAllocationQueryParams);
        ExcelUtil<HotelRoomAllocationExport> util = new ExcelUtil<>(HotelRoomAllocationExport.class);
        util.exportExcel(response, list, "导出房间分配记录");
    }

    @Log(title = "发送短信和邮件", businessType = BusinessType.SMS_MAIL)
    @PreAuthorize("@ss.hasPermi('ei:hotelRoomAllocation:sendSmsMail')")
    @PostMapping("/sendSmsMail")
    @Operation(summary = "发送短信和邮件")
    public AjaxResult sendSmsMail(@RequestBody HotelRoomSmsMailParam hotelRoomSmsMailParam) {
        if (hotelRoomSmsMailParam.getHotelRoomAllocationIds() == null) {
            return error("参数错误");
        }
        else {
            List<String> ids = hotelRoomSmsMailParam.getHotelRoomAllocationIds();
            return success(hotelRoomAllocationService.sendSmsMail(ids));
        }
    }

}
