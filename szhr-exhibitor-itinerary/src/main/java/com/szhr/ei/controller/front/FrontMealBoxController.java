package com.szhr.ei.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.MealBoxQueryParams;
import com.szhr.ei.converter.response.MealBoxVO;
import com.szhr.ei.domain.MealBox;
import com.szhr.ei.service.IMealBoxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 前端午饭盒饭选项管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/mealBox")
@Tag(name = "午饭盒饭选项管理")
public class FrontMealBoxController extends BaseController {

    @Resource
    private IMealBoxService mealBoxService;

    /**
     * 查询午饭盒饭选项列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询午饭盒饭选项列表")
    public TableDataInfo list(MealBoxQueryParams mealBoxQueryParams) {
        startPage();
        List<MealBoxVO> list = mealBoxService.selectVOList(mealBoxQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看午饭盒饭选项详情
     */
    @GetMapping("/{mealBoxId}")
    @Operation(summary = "查看午饭盒饭选项详情")
    public AjaxResult get(@PathVariable("mealBoxId") String mealBoxId) {
        return AjaxResult.success(mealBoxService.getById(mealBoxId));
    }



}
