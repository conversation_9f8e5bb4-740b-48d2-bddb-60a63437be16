package com.szhr.ei.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.domain.MealReservation;
import com.szhr.ei.service.IMealOptionService;
import com.szhr.ei.service.IMealReservationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 用户订餐信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/mealReservation")
@Tag(name = "用户订餐信息管理")
public class UserMealReservationController extends FrontBaseController {

    @Resource
    private IMealReservationService mealReservationService;

    @Resource
    private IMealOptionService mealOptionService;

    /**
     * 用户端投票选餐馆或自行解决（投票接口，午餐/晚餐通用）
     * mealType: 1=午餐，2=晚餐
     * isSelfHelp: 1=自行解决，0=集体聚餐
     * mealOptionId: 选择的餐饮套餐/餐馆ID（集体聚餐时必填）*/


    @PostMapping("/vote")
    @Operation(summary="用户端投票选餐馆或自行解决")
    @RepeatSubmit
    public AjaxResult vote(@Validated @RequestBody MealReservation mealReservation){
        //只允许操作自己的数据
        mealReservation.setFairUserId(getFairUserId());

        //查询套餐，判断投票是否已经截止
        MealOption mealOption=mealOptionService.getById(mealReservation.getMealOptionId());
        if(mealOption==null){
            return error("餐饮套餐不存在");
        }
        Date now=new Date();
        Date endTime=mealOption.getSelectionEndTime();
        if(endTime!=null&&now.after(endTime)){
            mealReservation.setIsSelfHelp(1);//超时默认自行解决
            mealReservation.setStatus(0);//状态“待确认”或超时
            mealReservation.setRemarks("超时未选择，默认自行解决");

        }else{
            if(mealReservation.getIsSelfHelp()==null){
                return error("请选择就餐方式");
            }
            //如果不是自行解决，必须选套餐
            if(mealReservation.getIsSelfHelp()==0&&StringUtils.isBlank(mealReservation.getMealOptionId())){
                return error("请选择餐饮套餐");
            }
            mealReservation.setStatus(0);//待确认
        }
        mealReservation.setCreateDt(now);
        //避免重复投票
        List<MealReservation> exists=mealReservationService.listByCondition(mealReservation);
        if(CollUtil.isNotEmpty(exists)){
            return error("您已选餐，不可重复提交");
        }
        mealReservation.setMealReservationId(IdUtils.randomUUID());
        mealReservationService.save(mealReservation);
        return success("选择成功");

    }

    /**
     * 用户端午餐选套餐/盒饭
     * mealType=1, mealBoxId 选中盒饭ID
     */
    @PostMapping("/lunch")
    @Operation(summary = "用户端午餐选套餐/盒饭")
    @RepeatSubmit
    public AjaxResult lunch(@Validated @RequestBody MealReservation mealReservation) {
        Date now = DateUtils.getNowDate();
        mealReservation.setFairUserId(getFairUserId());
        mealReservation.setMealType(1);//午餐
        mealReservation.setCreateDt(now);
        //超时逻辑与投票一致
        MealOption mealOption=mealOptionService.getById(mealReservation.getMealOptionId());
        if(mealOption==null){
            return error("餐饮套餐不存在");
        }

        Date endTime=mealOption.getSelectionEndTime();
        if(endTime!=null&&now.after(endTime)){
            mealReservation.setIsSelfHelp(1);
            mealReservation.setRemarks("超时未选择，默认自行解决");
        }
        //检查是否已选
        List<MealReservation> exists=mealReservationService.listByCondition(mealReservation);
        if(CollUtil.isNotEmpty(exists)){
            return error("您已选餐");
        }
        mealReservation.setMealReservationId(IdUtils.randomUUID());
        mealReservationService.save(mealReservation);
        return success("午餐选餐成功");
    }

    /**
     * 用户端晚餐是否同行
     * mealType=2, isSelfHelp  1自行解决 0参加集体
     */
    @PostMapping("/dinner")
    @Operation(summary="用户端晚餐选是否同行")
    @RepeatSubmit
    public AjaxResult dinner(@Validated @RequestBody MealReservation mealReservation){
        Date now = DateUtils.getNowDate();
        mealReservation.setFairUserId(getFairUserId());
        mealReservation.setMealType(2);//晚餐
        mealReservation.setCreateDt(now);
        //超时逻辑与/vote一致
        MealOption mealOption = mealOptionService.getById(mealReservation.getMealOptionId());
        if(mealOption==null){
            return error("餐饮信息不存在");
        }

        Date endTime=mealOption.getSelectionEndTime();
        if(endTime != null && now.after(endTime)) {
            mealReservation.setIsSelfHelp(1);
            mealReservation.setRemarks("超时未选择，默认自行解决");
        }
        List<MealReservation> exists = mealReservationService.listByCondition(mealReservation);
        if(CollUtil.isNotEmpty(exists)){
            return error("您已选晚餐，不可重复提交");
        }
        mealReservation.setMealReservationId(IdUtils.randomUUID());
        mealReservationService.save(mealReservation);
        return success("晚餐选餐成功");
    }


    /**
     * 新增用户订餐信息
     */

    @PostMapping("/add")
    @Operation(summary = "新增用户订餐信息")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody MealReservation mealReservation) {
        List<MealReservation> list = mealReservationService.listByCondition(mealReservation);
        if (CollUtil.isNotEmpty(list)) {
            return error("该用户订餐信息已存在");
        }

        mealReservation.setMealReservationId(IdUtils.randomUUID());
        return toAjax(mealReservationService.save(mealReservation));
    }

    /**
     * 更新用户订餐信息
     */


    @PostMapping("/update")
    @Operation(summary = "更新用户订餐信息")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody MealReservation mealReservation) {
        if (StringUtils.isBlank(mealReservation.getMealReservationId())) {
            return error("参数错误");
        }
        MealReservation queryMealReservation = mealReservationService.getById(mealReservation.getMealReservationId());
        if (queryMealReservation == null) {
            return error("用户订餐信息不存在");
        }

        return toAjax(mealReservationService.updateById(mealReservation));
    }

    /**
     * 删除用户订餐信息
     */


    @DeleteMapping("/{mealReservationId}")
    @Operation(summary = "删除用户订餐信息")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "mealReservationId", required = true) String mealReservationId) {
        if (StringUtils.isBlank(mealReservationId)) {
            return error("参数错误");
        }
        MealReservation queryMealReservation = mealReservationService.getById(mealReservationId);
        if (queryMealReservation == null) {
            return error("用户订餐信息不存在");
        }

        return toAjax(mealReservationService.removeById(mealReservationId));
    }

    /**
     * 查询用户订餐信息列表
     */

    @GetMapping("/list")
    @Operation(summary = "查询用户订餐信息列表")
    public TableDataInfo list(MealReservation mealReservation) {
        startPage();
        mealReservation.setFairUserId(getFairUserId());
        List<MealReservation> list = mealReservationService.listByCondition(mealReservation);
        return getDataTable(list);
    }

    /**
     * 查看用户订餐信息详情
     */

    @GetMapping("/{id}")
    @Operation(summary = "查看用户订餐信息详情")
    public AjaxResult get(@PathVariable("id") String id) {
        return AjaxResult.success(mealReservationService.getById(id));
    }


    /**
     * 导出用户订餐信息
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, MealReservation mealReservation) {
        List<MealReservation> list = mealReservationService.listByCondition(mealReservation);
        ExcelUtil<MealReservation> util = new ExcelUtil<MealReservation>(MealReservation.class);
        util.exportExcel(response, list, "导出用户订餐信息");
    }


}
