package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.DictUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.HotelConvert;
import com.szhr.ei.converter.response.HotelStatVO;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.converter.response.HotelVO;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IHotelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 酒店管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/hotel")
@Tag(name = "酒店管理")
public class HotelController extends BaseController {

    @Resource
    private IHotelService hotelService;

    @Resource
    private IActivityService activityService;

    @Autowired
    private HotelConvert hotelConvert;

    /**
     * 新增酒店
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:add')")
    @Log(title = "新增酒店", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增酒店")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody Hotel hotel) {
        Hotel hotelParams = new Hotel();
        hotelParams.setActivityId(hotel.getActivityId());
        hotelParams.setName(hotel.getName());
        List<HotelVO> list = hotelService.listByCondition(hotelParams);
        if (CollUtil.isNotEmpty(list)) {
            return error("该酒店已存在");
        }

        hotel.setHotelId(IdUtils.randomUUID());
        hotel.setCreateDt(DateUtils.getNowDate());
        return toAjax(hotelService.save(hotel));
    }

    /**
     * 更新酒店
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:update')")
    @Log(title = "更新酒店", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新酒店")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody Hotel hotel) {
        if (StringUtils.isBlank(hotel.getHotelId())) {
            return error("参数错误");
        }
        Hotel queryHotel = hotelService.getById(hotel.getHotelId());
        if (queryHotel == null) {
            return error("酒店不存在");
        }

        return toAjax(hotelService.updateById(hotel));
    }

    /**
     * 删除酒店
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:delete')")
    @Log(title = "删除酒店", businessType = BusinessType.DELETE)
    @DeleteMapping("/{hotelId}")
    @Operation(summary = "删除酒店")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable("hotelId") String hotelId) {
        if (StringUtils.isBlank(hotelId)) {
            return error("参数错误");
        }
        Hotel queryHotel = hotelService.getById(hotelId);
        if (queryHotel == null) {
            return error("酒店不存在");
        }

        return toAjax(hotelService.removeById(hotelId));
    }

    /**
     * 查询酒店列表
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:list')")
    @GetMapping("/list")
    @Operation(summary = "查询酒店列表")
    public TableDataInfo list(Hotel hotel) {
        logger.debug("hotel: {}", JSON.toJSONString(hotel));
        startPage();
        List<HotelVO> list = hotelService.listByCondition(hotel);
        return getDataTable(list);
    }

    /**
     * 查看酒店详情
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:get')")
    @GetMapping("/{hotelId}")
    @Operation(summary = "查看酒店详情")
    public AjaxResult get(@PathVariable("hotelId") String hotelId) {
        HotelVO hotelVO = null;
        Hotel hotel = hotelService.getById(hotelId);
        if (hotel != null) {
            hotelVO = hotelConvert.toVo(hotel);
            Activity activity = activityService.getById(hotel.getActivityId());
            hotelVO.setActivityName(activity.getActivityName());
            return AjaxResult.success(hotelVO);
        }
        return error("酒店不存在");
    }

    /**
     * 上传酒店图片
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:uploadImg')")
    @Log(title = "上传酒店图片", businessType = BusinessType.UPLOAD)
    @PostMapping("/upload/{hotelId}")
    @Operation(summary = "上传酒店图片")
    public AjaxResult upload(@PathVariable(value = "hotelId", required = true) String hotelId,
                             @RequestParam(value = "file", required = true) MultipartFile file) {
        try {
            String uploadUrl = FileUploadUtils.uploadMinio(FilePrefixConstants.HOTEL, file);
            if (StringUtils.isNotBlank(uploadUrl)) {
                Hotel hotel = hotelService.getById(hotelId);
                hotel.setImgUrl(uploadUrl);
                return toAjax(hotelService.updateById(hotel));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return error("上传失败");
    }

    /**
     * 删除酒店图片
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:removeImg')")
    @Log(title = "删除酒店图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{hotelId}")
    public AjaxResult remove(@PathVariable(value = "hotelId", required = true) String hotelId) {
        Hotel hotel = hotelService.getById(hotelId);
        if (hotel == null) {
            return error("删除失败");
        }
        String imgUrl = hotel.getImgUrl();
        if (StringUtils.isBlank(imgUrl)) {
            return error("删除失败");
        }
        boolean updateFlag = hotelService.setImgUrlToNull(hotelId);
        if(updateFlag) {
            try {
                MinioUtil.removeObject(imgUrl);
                return success("删除成功");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return error("删除失败");
    }

    /**
     * 查询酒店入住统计列表
     */
    @PreAuthorize("@ss.hasPermi('ei:hotel:queryStat')")
    @GetMapping("/queryStat")
    @Operation(summary = "查询酒店入住统计列表")
    public TableDataInfo queryStat(Hotel hotel) {
        startPage();
        List<HotelStatVO> list = hotelService.queryHotelStatVOList(hotel);
        return getDataTable(list);
    }

}
