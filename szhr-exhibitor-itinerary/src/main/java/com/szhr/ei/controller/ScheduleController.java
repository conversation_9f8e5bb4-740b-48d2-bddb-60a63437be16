package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.ScheduleConvert;
import com.szhr.ei.converter.response.ScheduleVO;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.Schedule;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 日程安排信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/schedule")
@Tag(name = "日程安排信息管理")
public class ScheduleController extends BaseController {

    @Resource
    private IScheduleService scheduleService;

    @Resource
    private IActivityService activityService;

    @Autowired
    private ScheduleConvert scheduleConvert;

    /**
     * 新增日程安排信息
     */
    @PreAuthorize("@ss.hasPermi('ei:schedule:add')")
    @Log(title = "新增日程安排信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增日程安排信息")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody Schedule schedule) {
        List<ScheduleVO> list = scheduleService.listByCondition(schedule);
        if (CollUtil.isNotEmpty(list)) {
            return error("该日程安排信息已存在");
        }

        schedule.setScheduleId(IdUtils.randomUUID());
        return toAjax(scheduleService.save(schedule));
    }

    /**
     * 更新日程安排信息
     */
    @PreAuthorize("@ss.hasPermi('ei:schedule:update')")
    @Log(title = "更新日程安排信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新日程安排信息")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody Schedule schedule) {
        if (StringUtils.isBlank(schedule.getScheduleId())) {
            return error("参数错误");
        }
        Schedule querySchedule = scheduleService.getById(schedule.getScheduleId());
        if (querySchedule == null) {
            return error("日程安排信息不存在");
        }

        return toAjax(scheduleService.updateById(schedule));
    }

    /**
     * 删除日程安排信息
     */
    @PreAuthorize("@ss.hasPermi('ei:schedule:delete')")
    @Log(title = "删除日程安排信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scheduleId}")
    @Operation(summary = "删除日程安排信息")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "scheduleId", required = true) String scheduleId) {
        if (StringUtils.isBlank(scheduleId)) {
            return error("参数错误");
        }
        Schedule querySchedule = scheduleService.getById(scheduleId);
        if (querySchedule == null) {
            return error("日程安排信息不存在");
        }

        return toAjax(scheduleService.removeById(scheduleId));
    }

    /**
     * 查询日程安排信息列表
     */
    @PreAuthorize("@ss.hasPermi('ei:schedule:list')")
    @GetMapping("/list")
    @Operation(summary = "查询日程安排信息列表")
    public TableDataInfo list(Schedule schedule) {
        startPage();
        List<ScheduleVO> list = scheduleService.listByCondition(schedule);
        return getDataTable(list);
    }

    /**
     * 查看日程安排信息详情
     */
    @PreAuthorize("@ss.hasPermi('ei:schedule:get')")
    @GetMapping("/{scheduleId}")
    @Operation(summary = "查看日程安排信息详情")
    public AjaxResult get(@PathVariable("scheduleId") String scheduleId) {
        ScheduleVO scheduleVO = null;
        Schedule schedule = scheduleService.getById(scheduleId);
        if (schedule != null) {
            scheduleVO = scheduleConvert.toVo(schedule);
            Activity activity = activityService.getById(schedule.getActivityId());
            if(activity!= null){
                scheduleVO.setActivityName(activity.getActivityName());
            }
            return AjaxResult.success(scheduleVO);
        }
        return error("日程安排不存在");
    }
    /**
     * 上传酒店图片

     @PreAuthorize("@ss.hasPermi('ei:schedule:uploadImg')")
     @Log(title = "上传日程安排信息图片", businessType = BusinessType.UPLOAD)
     @PostMapping("/upload/{id}")
     @Operation(summary = "上传日程安排信息图片")
     public AjaxResult upload(@PathVariable(value = "id", required = true) String id,
     @RequestParam(value = "file", required = true) MultipartFile file) {
     try {
     String uploadUrl = FileUploadUtils.uploadMinio(FilePrefixConstants.SCHEDULE, file);
     if (StringUtils.isNotBlank(uploadUrl)) {
     Schedule schedule = scheduleService.getById(id);
     schedule.setPhotoUrl(uploadUrl);
     return toAjax(scheduleService.updateById(schedule));
     }
     } catch (IOException e) {
     throw new RuntimeException(e);
     }
     return error("上传失败");
     }

     /**
      * 删除日程安排信息图片

     @PreAuthorize("@ss.hasPermi('ei:schedule:removeImg')")
     @Log(title = "删除日程安排信息图片", businessType = BusinessType.DELETE)
     @DeleteMapping("/remove/{id}")
     public AjaxResult remove(@PathVariable(value = "id", required = true) String id) {
     Schedule schedule = scheduleService.getById(id);
     if (schedule == null) {
     return error("删除失败");
     }
     String imgUrl = schedule.getPhotoUrl();
     if (StringUtils.isBlank(imgUrl)) {
     return error("删除失败");
     }
     boolean updateFlag = scheduleService.setImgUrlToNull(id);
     if(updateFlag) {
     try {
     MinioUtil.removeObject(imgUrl);
     return success("删除成功");
     } catch (Exception e) {
     throw new RuntimeException(e);
     }
     }
     return error("删除失败");
     }
     */


}
