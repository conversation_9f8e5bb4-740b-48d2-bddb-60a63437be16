package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.MealBoxQueryParams;
import com.szhr.ei.converter.response.MealBoxVO;
import com.szhr.ei.domain.MealBox;
import com.szhr.ei.service.IMealBoxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 午饭盒饭选项管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/mealBox")
@Tag(name = "午饭盒饭选项管理")
public class MealBoxController extends BaseController {

    @Resource
    private IMealBoxService mealBoxService;

    /**
     * 新增午饭盒饭选项
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:add')")
    @Log(title = "新增午饭盒饭选项", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增午饭盒饭选项")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody MealBox mealBox) {
        List<MealBox> list = mealBoxService.listByCondition(mealBox);
        if (CollUtil.isNotEmpty(list)) {
            return error("该午饭盒饭选项已存在");
        }

        mealBox.setMealBoxId(IdUtils.randomUUID());
        return toAjax(mealBoxService.save(mealBox));
    }

    /**
     * 更新午饭盒饭选项
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:update')")
    @Log(title = "更新午饭盒饭选项", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新午饭盒饭选项")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody MealBox mealBox) {
        if (StringUtils.isBlank(mealBox.getMealBoxId())) {
            return error("参数错误");
        }
        MealBox queryMealBox = mealBoxService.getById(mealBox.getMealBoxId());
        if (queryMealBox == null) {
            return error("午饭盒饭选项不存在");
        }

        return toAjax(mealBoxService.updateById(mealBox));
    }

    /**
     * 删除午饭盒饭选项
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:delete')")
    @Log(title = "删除午饭盒饭选项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除午饭盒饭选项")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "id", required = true) String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        MealBox queryMealBox = mealBoxService.getById(id);
        if (queryMealBox == null) {
            return error("午饭盒饭选项不存在");
        }

        return toAjax(mealBoxService.removeById(id));
    }

    /**
     * 查询午饭盒饭选项列表
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:list')")
    @GetMapping("/list")
    @Operation(summary = "查询午饭盒饭选项列表")
    public TableDataInfo list(MealBoxQueryParams mealBoxQueryParams) {
        startPage();
        List<MealBoxVO> list = mealBoxService.selectVOList(mealBoxQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看午饭盒饭选项详情
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:get')")
    @GetMapping("/{id}")
    @Operation(summary = "查看午饭盒饭选项详情")
    public AjaxResult get(@PathVariable("id") String id) {
        return AjaxResult.success(mealBoxService.getById(id));
    }

    /**
     * 上传酒店图片
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:uploadImg')")
    @Log(title = "上传酒店图片", businessType = BusinessType.UPLOAD)
    @PostMapping("/upload/{id}")
    @Operation(summary = "上传酒店图片")
    public AjaxResult upload(@PathVariable(value = "id", required = true) String id,
                             @RequestParam(value = "file", required = true) MultipartFile file) {
        try {
            String uploadUrl = FileUploadUtils.uploadMinio(FilePrefixConstants.MEAL_BOX, file);
            if (StringUtils.isNotBlank(uploadUrl)) {
                MealBox hotel = mealBoxService.getById(id);
                hotel.setPhotoUrl(uploadUrl);
                return toAjax(mealBoxService.updateById(hotel));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return error("上传失败");
    }

    /**
     * 删除酒店图片
     */
    @PreAuthorize("@ss.hasPermi('ei:mealBox:removeImg')")
    @Log(title = "删除酒店图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable(value = "id", required = true) String id) {
        MealBox hotel = mealBoxService.getById(id);
        if (hotel == null) {
            return error("删除失败");
        }
        String imgUrl = hotel.getPhotoUrl();
        if (StringUtils.isBlank(imgUrl)) {
            return error("删除失败");
        }
        boolean updateFlag = mealBoxService.setImgUrlToNull(id);
        if(updateFlag) {
            try {
                MinioUtil.removeObject(imgUrl);
                return success("删除成功");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return error("删除失败");
    }


}
