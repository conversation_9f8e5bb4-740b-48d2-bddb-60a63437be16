package com.szhr.ei.controller;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.DataDictUtils;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.FairUserParams;
import com.szhr.ei.converter.response.FairUserVO;
import com.szhr.ei.domain.ActivityCompany;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.service.IActivityCompanyService;
import com.szhr.ei.service.IActivityInvitationService;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IFairUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 招展活动行程人员信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/fairUser")
@Tag(name = "行程人员信息管理")
public class FairUserController extends BaseController {
    @Resource
    private IFairUserService fairUserService;

    @Resource
    private IActivityCompanyService activityCompanyService;

    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityInvitationService activityInvitationService;

    /**
     * 获取邀约库列表
     */
    @PreAuthorize("@ss.hasPermi('ei:fairUser:list')")
    @GetMapping("/list")
    @Operation(summary = "查询行程人员列表")
    public TableDataInfo list(FairUserParams fairUserParams) {
        startPage();
        return getDataTable(fairUserService.selectFairUserList(fairUserParams));
    }

    @Operation(summary = "下载行程人员导入模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String[] fields = {"companyName", "personName", "gender", "mobilePhone", "certTypeCode", "certNum"};
        ExcelUtil<FairUserVO> util = new ExcelUtil<>(FairUserVO.class);
        util.showColumn(fields);
        util.importTemplateInit(response, "活动行程人员导入");
        util.setDropdownValidation(DataDictUtils.getDictValues("GENDER"), 1, 1, 3000);
        util.setDropdownValidation(getDictLabelArr("ei_cert_type_code"), 3, 1, 3000);
        util.exportExcel(response);
    }

    @Log(title = "行程人员信息导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('ei:fairUser:import')")
    @PostMapping("/importData")
    @Operation(summary = "行程人员信息导入")
    public AjaxResult importData(MultipartFile file, String activityId) throws Exception {
        if (activityService.getById(activityId) == null) {
            return error("活动不存在！");
        }
        ExcelUtil<FairUserVO> util = new ExcelUtil<>(FairUserVO.class);
        List<FairUserVO> fairUserList = util.importExcel(file.getInputStream());
        if (fairUserList == null || fairUserList.isEmpty()) {
            return error("导入数据不能为空！");
        }
        Date date = DateUtils.getNowDate();
        //检查公司名称是否已经存在邀约库
        List<String> notExistInvitationCompanyNameList = new ArrayList<>();
        //检查公司名称是否未存在单位库里
        List<String> notExistCompanyNameList = new ArrayList<>();
        //检查手机号是否已经存在行程列表里
        List<String> existMobilePhoneList = new ArrayList<>();
        List<FairUser> needAddFairUserList = new ArrayList<>();
        for (FairUserVO fairUserVO : fairUserList) {

            if (!Validator.isMobile(fairUserVO.getMobilePhone())) {
                return error("个人手机号码校验错误");
            }

            if (!Validator.isEmail(fairUserVO.getEmail())) {
                return error("邮箱格式校验错误");
            }

            if (StringUtils.equals(fairUserVO.getCertTypeCode(), DictOptionConstans.CERT_TYPE_IDCARD)) {
                if (!IdcardUtil.isValidCard(fairUserVO.getCertNum())) {
                    return error("个人身份证件号码校验错误");
                }
            }

            //添加到邀约库列表
            FairUser fairUser = new FairUser();
            LambdaQueryWrapper<ActivityCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityCompany::getCompanyName, fairUserVO.getCompanyName())
                    .isNull(ActivityCompany::getDeleteDt);

            ActivityCompany existActivityCompany = activityCompanyService.getOne( wrapper);
            if (existActivityCompany == null) {
                notExistCompanyNameList.add(fairUserVO.getCompanyName());
            } else {
                fairUser.setActivityCompanyId(existActivityCompany.getId());
                //检查公司是否未在邀约库里
                LambdaQueryWrapper<ActivityInvitation> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(ActivityInvitation::getActivityCompanyId, existActivityCompany.getId())
                        .eq(ActivityInvitation::getActivityId, activityId)
                        .isNull(ActivityInvitation::getDeleteDt);
                if (activityInvitationService.count(wrapper2) == 0) {
                    notExistInvitationCompanyNameList.add(fairUserVO.getCompanyName());
                }
            }
            //检查手机号码是否已经存在
            LambdaQueryWrapper<FairUser> fairUserWrapper = new LambdaQueryWrapper<>();
            fairUserWrapper.eq(FairUser::getMobilePhone, AESTypeHandler.encrypt(fairUserVO.getMobilePhone()))
                    .eq(FairUser::getActivityId, activityId)
                    .isNull(FairUser::getDeleteDt);
            if (fairUserService.count(fairUserWrapper) > 0) {
                existMobilePhoneList.add(fairUserVO.getMobilePhone());
            }
            fairUser.setGender(fairUserVO.getGender());
            fairUser.setMobilePhone(fairUserVO.getMobilePhone());
            fairUser.setPersonName(fairUserVO.getPersonName());
            fairUser.setCertNum(fairUserVO.getCertNum());
            fairUser.setCertTypeCode(fairUserVO.getCertTypeCode());
            fairUser.setFairUserId(IdUtils.randomUUID());
            fairUser.setActivityId(activityId);
            fairUser.setCreateDt(date);
            fairUser.setCreatedBy(getUserId());
            fairUser.setLastUpdatedBy(getUserId());
            fairUser.setLastUpdateDt(DateUtils.getNowDate());
            fairUser.setEmail(fairUserVO.getEmail());
            needAddFairUserList.add(fairUser);
        }
        String errorMsg = "";
        if (!notExistCompanyNameList.isEmpty()) {
            errorMsg += "以下单位名称未存在单位基础信息，请从单位信息管理添加单位信息：" + StringUtils.join(notExistCompanyNameList, ",");
            errorMsg += "。";
        }
        if (!notExistInvitationCompanyNameList.isEmpty()) {
            errorMsg += "以下单位名称未在邀约库里：" + StringUtils.join(notExistInvitationCompanyNameList, ",");
            errorMsg += "。";
        }
        if (!existMobilePhoneList.isEmpty()) {
            errorMsg += "以下手机号码已经存在该活动行程人员里：" + StringUtils.join(existMobilePhoneList, ",");
            errorMsg += "。";
        }
        if (!StringUtils.isEmpty(errorMsg)) {
            return error(errorMsg);
        }
        if(needAddFairUserList.isEmpty()) {
            return success("无新增邀约库数据！");
        }
        fairUserService.saveBatch(needAddFairUserList);
        return success("导入成功！");
    }

    @PreAuthorize("@ss.hasPermi('ei:fairUser:add')")
    @Log(title = "新增行程人员信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增行程人员信息")
    public AjaxResult add(@Validated @RequestBody FairUser fairUser) {

        if (StringUtils.equals(fairUser.getCertTypeCode(), DictOptionConstans.CERT_TYPE_IDCARD)) {
            if (!IdcardUtil.isValidCard(fairUser.getCertNum())) {
                return error("个人身份证件号码校验错误");
            }
        }

        if (!Validator.isEmail(fairUser.getEmail())) {
            return error("邮箱格式校验错误");
        }

        //判断单位是否在邀约库列表里
        LambdaQueryWrapper<ActivityInvitation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityInvitation::getActivityId, fairUser.getActivityId())
                .eq(ActivityInvitation::getActivityCompanyId, fairUser.getActivityCompanyId())
                .isNull(ActivityInvitation::getDeleteDt);
        if (activityInvitationService.count(wrapper) == 0) {
            return error("该单位名称未在邀约库里");
        }
        LambdaQueryWrapper<FairUser> fairUserWrapper = new LambdaQueryWrapper<>();
        fairUserWrapper.eq(FairUser::getMobilePhone, AESTypeHandler.encrypt(fairUser.getMobilePhone()))
                .eq(FairUser::getActivityId, fairUser.getActivityId())
                .isNull(FairUser::getDeleteDt);
        if (fairUserService.count(fairUserWrapper) > 0) {
            return error("手机号码已存在该活动里");
        }

        fairUser.setFairUserId(IdUtils.randomUUID());
        fairUser.setCreateDt(DateUtils.getNowDate());
        fairUser.setCreatedBy(getUserId());
        fairUser.setLastUpdateDt(fairUser.getCreateDt());
        fairUser.setLastUpdatedBy(fairUser.getCreatedBy());
        fairUser.setEmail(fairUser.getEmail());
        return toAjax(fairUserService.save(fairUser));
    }

    @PreAuthorize("@ss.hasPermi('ei:fairUser:edit')")
    @Log(title = "编辑行程人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @Operation(summary = "编辑行程人员信息")
    public AjaxResult edit(@Validated @RequestBody FairUser fairUser) {

        if (StringUtils.equals(fairUser.getCertTypeCode(), DictOptionConstans.CERT_TYPE_IDCARD)) {
            if (!IdcardUtil.isValidCard(fairUser.getCertNum())) {
                return error("个人身份证件号码校验错误");
            }
        }

        if (!Validator.isEmail(fairUser.getEmail())) {
            return error("邮箱格式校验错误");
        }

        FairUser originFairUser = fairUserService.getById(fairUser.getFairUserId());
        if (originFairUser == null) {
            return error("该行程人员信息不存在");
        }

        FairUser updateFairUser = new FairUser();
        if(!StringUtils.equals(fairUser.getMobilePhone(), originFairUser.getMobilePhone())) {
            LambdaQueryWrapper<FairUser> fairUserWrapper = new LambdaQueryWrapper<>();
            fairUserWrapper.eq(FairUser::getMobilePhone, AESTypeHandler.encrypt(fairUser.getMobilePhone()))
                    .eq(FairUser::getActivityId, originFairUser.getActivityId())
                    .isNull(FairUser::getDeleteDt);
            if (fairUserService.count(fairUserWrapper) > 0) {
                return error("新修改的手机号码已存在该活动里");
            }
            updateFairUser.setMobilePhone(fairUser.getMobilePhone());
        }
        if(StringUtils.isNotBlank(fairUser.getActivityCompanyId()) && !StringUtils.equals(fairUser.getActivityCompanyId(), originFairUser.getActivityCompanyId())) {
            //判断单位是否在邀约库列表里
            LambdaQueryWrapper<ActivityInvitation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ActivityInvitation::getActivityId, originFairUser.getActivityId())
                    .eq(ActivityInvitation::getActivityCompanyId, fairUser.getActivityCompanyId())
                    .isNull(ActivityInvitation::getDeleteDt);
            if (activityInvitationService.count(wrapper) == 0) {
                return error("新修改的单位未在邀约库里");
            }
            updateFairUser.setActivityCompanyId(fairUser.getActivityCompanyId());
        }
        updateFairUser.setFairUserId(fairUser.getFairUserId());
        updateFairUser.setPersonName(fairUser.getPersonName());
        updateFairUser.setCertNum(fairUser.getCertNum());
        updateFairUser.setCertTypeCode(fairUser.getCertTypeCode());
        updateFairUser.setGender(fairUser.getGender());
        updateFairUser.setLastUpdatedBy(getUserId());
        updateFairUser.setLastUpdateDt(DateUtils.getNowDate());
        updateFairUser.setEmail(fairUser.getEmail());
        return toAjax(fairUserService.updateById(updateFairUser));
    }

    /**
     * 删除行程人员信息
     */
    @PreAuthorize("@ss.hasPermi('ei:fairUser:remove')")
    @Log(title = "删除行程人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    @Operation(summary = "删除行程人员信息")
    public AjaxResult remove(@PathVariable String id) {
        FairUser queryFairUser = fairUserService.getById(id);
        if (queryFairUser == null || queryFairUser.getDeleteDt() != null) {
            return error("行程人员信息不存在");
        }
        FairUser fairUser = new FairUser();
        fairUser.setFairUserId(id);
        fairUser.setDeletedBy(getUserId());
        fairUser.setDeleteDt(DateUtils.getNowDate());
        return toAjax(fairUserService.updateById(fairUser));
    }

    @Log(title = "导出行程人员信息", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:fairUser:export')")
    @PostMapping("/export")
    @Operation(summary = "导出行程人员信息")
    public void export(HttpServletResponse response, FairUserParams fairUserParams) {
        List<FairUserVO> list = new ArrayList<>();
        if(StringUtils.isNotBlank(fairUserParams.getActivityId())) {
            list = fairUserService.selectFairUserList(fairUserParams);
        }
        ExcelUtil<FairUserVO> util = new ExcelUtil<>(FairUserVO.class);
        util.exportExcel(response, list, "行程人员信息");
    }

}
