package com.szhr.ei.controller.front;

import com.szhr.common.core.controller.BaseController;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.service.IFairUserService;
import com.szhr.framework.security.context.FairUserRequestContext;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * 控制器个人信息管理的基类
 */
@RestController
public class FrontBaseController extends BaseController {

    @Resource
    private IFairUserService fairUserService;

    @Resource
    private FairUserRequestContext fairUserRequestContext;

    protected String getFairUserId() {
        return fairUserRequestContext.getFairUserId();
    }

    protected String getActivityId() {
        return fairUserRequestContext.getActivityId();
    }

    protected FairUser getFairUser() {
        FairUser o = null;
        try {
            String fairUserId = getFairUserId();
            o = fairUserService.getById(fairUserId);
        } catch (Exception e) {
            logger.error("******** FairUserRequestContext getFairUserId Exception ********", e);
        }
        return o;
    }

}
