package com.szhr.ei.controller.front;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.constant.Constants;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.ei.converter.request.FairUserLoginParams;
import com.szhr.ei.service.IFairUserLoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行程人员登录管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/fairUser")
@Tag(name = "行程人员登录管理")
public class FairUserLoginController extends BaseController {

    @Resource
    private IFairUserLoginService fairUserLoginService;

    /**
     * 前端用户登录
     * @param fairUserLoginParams 登录信息
     * @return 结果
     */
    @PostMapping("/smslogin")
    @Operation(summary = "前端用户登录")
    public AjaxResult login(@Validated @RequestBody FairUserLoginParams fairUserLoginParams) {
        //logger.debug("######## 前端用户登录：{}", JSON.toJSONString(fairUserLoginParams));
        String mobilePhone = fairUserLoginParams.getMobilePhone();
        if (!Validator.isMobile(mobilePhone)) {
            return error("个人手机号码校验错误");
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = fairUserLoginService.fairUserLogin(fairUserLoginParams);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
}
