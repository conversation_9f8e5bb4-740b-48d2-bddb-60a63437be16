package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.domain.RoomType;
import com.szhr.ei.service.IRoomTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房型信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/roomType")
@Tag(name = "房型信息管理")
public class RoomTypeController extends BaseController {

    @Resource
    private IRoomTypeService roomTypeService;

    /**
     * 新增房型信息
     */
    @PreAuthorize("@ss.hasPermi('ei:roomType:add')")
    @Log(title = "新增房型信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增房型信息")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody RoomType roomType) {
        List<RoomType> list = roomTypeService.listByCondition(roomType);
        if (CollUtil.isNotEmpty(list)) {
            return error("该房型信息已存在");
        }

        roomType.setId(IdUtils.randomUUID());
        logger.info("roomType: {} ", JSON.toJSONString(roomType));
        return toAjax(roomTypeService.save(roomType));
    }

    /**
     * 更新房型信息
     */
    @PreAuthorize("@ss.hasPermi('ei:roomType:update')")
    @Log(title = "更新房型信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新房型信息")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody RoomType roomType) {
        if (StringUtils.isBlank(roomType.getId())) {
            return error("参数错误");
        }
        RoomType queryRoomType = roomTypeService.getById(roomType.getId());
        if (queryRoomType == null) {
            return error("房型信息不存在");
        }

        return toAjax(roomTypeService.updateById(roomType));
    }

    /**
     * 删除房型信息
     */
    @PreAuthorize("@ss.hasPermi('ei:roomType:delete')")
    @Log(title = "删除房型信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除房型信息")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "id", required = true) String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        RoomType queryRoomType = roomTypeService.getById(id);
        if (queryRoomType == null) {
            return error("房型信息不存在");
        }

        return toAjax(roomTypeService.removeById(id));
    }

    /**
     * 查询房型信息列表
     */
    @PreAuthorize("@ss.hasPermi('ei:roomType:list')")
    @GetMapping("/list")
    @Operation(summary = "查询房型信息列表")
    public TableDataInfo list(RoomType roomType) {
        startPage();
        List<RoomType> list = roomTypeService.listByCondition(roomType);
        return getDataTable(list);
    }

    /**
     * 查看房型信息详情
     */
    @PreAuthorize("@ss.hasPermi('ei:roomType:get')")
    @GetMapping("/{id}")
    @Operation(summary = "查看房型信息详情")
    public AjaxResult get(@PathVariable("id") String id) {
        return AjaxResult.success(roomTypeService.getById(id));
    }


}
