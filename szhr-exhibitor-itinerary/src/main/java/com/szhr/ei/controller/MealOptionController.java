package com.szhr.ei.controller;

import cn.hutool.core.collection.CollUtil;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.MealOptionConvert;
import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.service.IMealOptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 餐饮套餐信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/mealOption")
@Tag(name = "餐饮套餐信息管理")
public class MealOptionController extends BaseController {

    @Resource
    private IMealOptionService mealOptionService;

    @Resource
    private MealOptionConvert mealOptionConvert;

    /**
     * 新增餐饮套餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:add')")
    @Log(title = "新增餐饮套餐信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增餐饮套餐信息")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody MealOption mealOption) {
        List<MealOption> list = mealOptionService.listByCondition(mealOption);
        if (CollUtil.isNotEmpty(list)) {
            return error("该餐饮套餐信息已存在");
        }

        mealOption.setMealOptionId(IdUtils.randomUUID());
        return toAjax(mealOptionService.save(mealOption));
    }

    /**
     * 更新餐饮套餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:update')")
    @Log(title = "更新餐饮套餐信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新餐饮套餐信息")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody MealOption mealOption) {
        if (StringUtils.isBlank(mealOption.getMealOptionId())) {
            return error("参数错误");
        }
        MealOption queryMealOption = mealOptionService.getById(mealOption.getMealOptionId());
        if (queryMealOption == null) {
            return error("餐饮套餐信息不存在");
        }

        return toAjax(mealOptionService.updateById(mealOption));
    }

    /**
     * 删除餐饮套餐信息
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:delete')")
    @Log(title = "删除餐饮套餐信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除餐饮套餐信息")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "id", required = true) String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        MealOption queryMealOption = mealOptionService.getById(id);
        if (queryMealOption == null) {
            return error("餐饮套餐信息不存在");
        }

        return toAjax(mealOptionService.removeById(id));
    }

    /**
     * 查询餐饮套餐信息列表
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:list')")
    @GetMapping("/list")
    @Operation(summary = "查询餐饮套餐信息列表")
    public TableDataInfo list(MealOptionQueryParams mealOptionQueryParams) {
        startPage();
        List<MealOptionVO> list = mealOptionService.selectVOList(mealOptionQueryParams);
        return getDataTable(list);
    }

    /**
     * 查看餐饮套餐信息详情
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:get')")
    @GetMapping("/{mealOptionId}")
    @Operation(summary = "查看餐饮套餐信息详情")
    public AjaxResult get(@PathVariable("mealOptionId") String mealOptionId) {
        logger.debug("MealOption get {} ", mealOptionId);
        return AjaxResult.success(mealOptionService.getById(mealOptionId));
    }

    /**
     * 上传餐饮套餐信息图片
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:uploadImg')")
    @Log(title = "上传餐饮套餐信息图片", businessType = BusinessType.UPLOAD)
    @PostMapping("/upload/{id}")
    @Operation(summary = "上传餐饮套餐信息图片")
    public AjaxResult upload(@PathVariable(value = "id", required = true) String id,
                             @RequestParam(value = "file", required = true) MultipartFile file) {
        try {
            String uploadUrl = FileUploadUtils.uploadMinio(FilePrefixConstants.MEAL_OPTION, file);
            if (StringUtils.isNotBlank(uploadUrl)) {
                MealOption mealOption = mealOptionService.getById(id);
                mealOption.setPhotoUrl(uploadUrl);
                return toAjax(mealOptionService.updateById(mealOption));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return error("上传失败");
    }


    /**
     * 删除餐饮套餐信息图片
     */
    @PreAuthorize("@ss.hasPermi('ei:mealOption:removeImg')")
    @Log(title = "删除餐饮套餐信息图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable(value = "id", required = true) String id) {
        MealOption mealOption = mealOptionService.getById(id);
        if (mealOption == null) {
            return error("删除失败");
        }
        String imgUrl = mealOption.getPhotoUrl();
        if (StringUtils.isBlank(imgUrl)) {
            return error("删除失败");
        }
        boolean updateFlag = mealOptionService.setImgUrlToNull(id);
        if(updateFlag) {
            try {
                MinioUtil.removeObject(imgUrl);
                return success("删除成功");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return error("删除失败");
    }

}
