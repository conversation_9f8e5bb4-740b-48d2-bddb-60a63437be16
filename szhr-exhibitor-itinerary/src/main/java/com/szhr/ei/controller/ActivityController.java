package com.szhr.ei.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.constant.FilePrefixConstants;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.AESUtils;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.file.FileUploadUtils;
import com.szhr.common.utils.file.MimeTypeUtils;
import com.szhr.common.utils.file.MinioUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.request.ActivityParams;
import com.szhr.ei.converter.response.ActivityVO;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.ActivityAttachment;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.service.IActivityAttachmentService;
import com.szhr.ei.service.IActivityInvitationService;
import com.szhr.ei.service.IActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 招展行程活动Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/activity")
@Tag(name = "活动管理")
public class ActivityController extends BaseController {
    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityAttachmentService activityAttachmentService;

    @Resource
    private IActivityInvitationService activityInvitationService;

    /**
     * 查询招展行程活动列表
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:list')")
    @GetMapping("/list")
    @Operation(summary = "查询招展行程活动列表")
    public TableDataInfo list(ActivityParams activity) {
        startPage();
        List<ActivityVO> list = activityService.selectActivityList(activity);
        return getDataTable(list);
    }

    /**
     * 新增招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:add')")
    @Log(title = "新增招展行程活动", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增招展行程活动")
    public AjaxResult add(@RequestBody Activity activity) {
        activity.setId(IdUtils.randomUUID());
        activity.setCreateDt(DateUtils.getNowDate());
        activity.setCreatedBy(getUserId());
        activity.setLastUpdateDt(activity.getCreateDt());
        activity.setLastUpdatedBy(activity.getCreatedBy());
        return toAjax(activityService.save(activity));
    }

    /**
     * 新增招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:uploadLogo')")
    @Log(title = "上传活动Logo", businessType = BusinessType.UPLOAD)
    @PostMapping("/uploadLogo")
    @Operation(summary = "上传活动Logo")
    public AjaxResult uploadLogo(String activityId,
                                 @RequestParam(value = "file", required = false) MultipartFile file) {
        Activity originalActivity = activityService.getById(activityId);
        if (originalActivity == null) {
            return error("活动不存在！");
        }
        Activity activity = new Activity();
        activity.setId(activityId);
        try {
            if (file != null) {
                String logoLink = FileUploadUtils.uploadMinio(FilePrefixConstants.ACTIVITY, file, MimeTypeUtils.IMAGE_EXTENSION);
                activity.setLogoLink(logoLink);
            } else {
                activity.setLogoLink("");
            }
            if (StringUtils.isNotBlank(originalActivity.getLogoLink())) {
                MinioUtil.removeObject(AESUtils.decrypt(originalActivity.getLogoLink()));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return toAjax(activityService.updateById(activity));
    }

    /**
     * 修改招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:edit')")
    @Log(title = "编辑招展行程活动", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @Operation(summary = "编辑招展行程活动")
    public AjaxResult edit(@RequestBody Activity activity) {
        activity.setLastUpdatedBy(getUserId());
        activity.setLastUpdateDt(DateUtils.getNowDate());
        return toAjax(activityService.updateById(activity));
    }

    /**
     * 获取招展行程活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:query')")
    @GetMapping(value = "/query/{id}")
    @Operation(summary = "获取招展行程活动详情")
    public AjaxResult query(@PathVariable("id") String id) {
        return success(activityService.getById(id));
    }

    /**
     * 删除招展行程活动
     */
    @PreAuthorize("@ss.hasPermi('ei:activity:remove')")
    @Log(title = "删除活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable String id) {
        Activity originalActivity = activityService.getById(id);
        if (originalActivity == null || originalActivity.getDeleteDt() != null) {
            return error("活动不存在！");
        }
        // 判断是否存在活动附件及邀约信息
        if (activityAttachmentService.count(new LambdaQueryWrapper<ActivityAttachment>().eq(ActivityAttachment::getActivityId, id)) > 0) {
            return error("该活动存在活动附件，不允许删除活动！");
        }
        if (activityInvitationService.count(new LambdaQueryWrapper<ActivityInvitation>().eq(ActivityInvitation::getActivityId, id).isNull(ActivityInvitation::getDeleteDt)) > 0) {
            return error("该活动存在邀约信息，不允许删除活动！");
        }
        Activity activity = new Activity();
        activity.setId(id);
        activity.setDeletedBy(getUserId());
        activity.setDeleteDt(DateUtils.getNowDate());
        return toAjax(activityService.updateById(activity));
    }

}
