package com.szhr.ei.controller.front;


import com.szhr.common.core.page.TableDataInfo;;
import com.szhr.ei.converter.request.ScheduleQueryParams;
import com.szhr.ei.converter.response.ScheduleVO;
import com.szhr.ei.service.IScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户日程管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/front/ei/userSchedule")
@Tag(name = "用户日程管理")
public class UserScheduleContrlloer extends FrontBaseController {

    @Resource
    IScheduleService scheduleService;

    /**
     * 查询日程安排
     */
    @GetMapping("/list")
    @Operation(summary = "查询日程安排")
    public TableDataInfo list(ScheduleQueryParams scheduleQueryParams) {
        startPage();
        List<ScheduleVO> list = scheduleService.selectScheduleByParams(scheduleQueryParams);
        return getDataTable(list);

    }
}