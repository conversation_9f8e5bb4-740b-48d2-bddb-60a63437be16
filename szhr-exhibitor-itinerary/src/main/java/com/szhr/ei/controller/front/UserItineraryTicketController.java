package com.szhr.ei.controller.front;

import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.service.IItineraryTicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 出行飞机或高铁班次记录管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/front/ei/itineraryTicket")
@Tag(name = "出行飞机或高铁班次记录管理")
public class UserItineraryTicketController extends FrontBaseController {

    @Resource
    private IItineraryTicketService itineraryTicketService;

    /**
     * 查询出行飞机或高铁班次记录列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询出行飞机或高铁班次记录列表")
    public TableDataInfo list(String activityId) {
        startPage();
        ItineraryTicketParams itineraryTicketParams = new ItineraryTicketParams();
        itineraryTicketParams.setActivityId(activityId);
        itineraryTicketParams.setFairUserId(getFairUserId());
        List<ItineraryTicketVO> list = itineraryTicketService.selectItineraryTicketVOList(itineraryTicketParams);
        return getDataTable(list);
    }

    /**
     * 查看出行飞机或高铁班次记录详情
     */
    @GetMapping("/{itineraryTicketId}")
    @Operation(summary = "查看出行飞机或高铁班次记录详情")
    public AjaxResult get(@PathVariable("itineraryTicketId") String itineraryTicketId) {
        return AjaxResult.success(itineraryTicketService.getById(itineraryTicketId));
    }

}
