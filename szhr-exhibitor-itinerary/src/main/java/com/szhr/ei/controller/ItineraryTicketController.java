package com.szhr.ei.controller;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szhr.common.annotation.Log;
import com.szhr.common.annotation.RepeatSubmit;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.controller.BaseController;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.core.page.TableDataInfo;
import com.szhr.common.enums.BusinessType;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.ItineraryTicketConvert;
import com.szhr.ei.converter.request.ItineraryTicketImportParams;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.domain.ItineraryTicket;
import com.szhr.ei.service.IFairUserService;
import com.szhr.ei.service.IItineraryTicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 出行飞机或高铁班次记录管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/ei/itineraryTicket")
@Tag(name = "出行飞机或高铁班次记录管理")
public class ItineraryTicketController extends BaseController {

    @Resource
    private IItineraryTicketService itineraryTicketService;

    @Resource
    private IFairUserService fairUserService;

    @Resource
    private ItineraryTicketConvert itineraryTicketConvert;

    /**
     * 新增出行飞机或高铁班次记录
     */
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:add')")
    @Log(title = "新增出行飞机或高铁班次记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增出行飞机或高铁班次记录")
    @RepeatSubmit
    public AjaxResult add(@Validated @RequestBody ItineraryTicketParams itineraryTicketParams) {

        String fairUserId = itineraryTicketParams.getFairUserId();

        LambdaQueryWrapper<ItineraryTicket> ticketWrapper = new LambdaQueryWrapper<>();
        ticketWrapper.eq(ItineraryTicket::getActivityId, itineraryTicketParams.getActivityId());
        ticketWrapper.eq(ItineraryTicket::getFairUserId, itineraryTicketParams.getFairUserId());
        ticketWrapper.eq(ItineraryTicket::getTicketDirection, itineraryTicketParams.getTicketDirection());
        if (itineraryTicketService.exists(ticketWrapper)) {
            return error("该出行飞机或高铁班次记录已存在");
        }

        ItineraryTicket itineraryTicket = itineraryTicketConvert.toPo(itineraryTicketParams);
        itineraryTicket.setItineraryTicketId(IdUtils.randomUUID());
        if (itineraryTicketParams.getStatus() != null) {
            itineraryTicket.setStatus(itineraryTicketParams.getStatus());
        } else {
            itineraryTicket.setStatus(DictOptionConstans.COMMON_STATUS_INIT);
        }

        return toAjax(itineraryTicketService.save(itineraryTicket));
    }

    /**
     * 更新出行飞机或高铁班次记录
     */
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:update')")
    @Log(title = "更新出行飞机或高铁班次记录", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Operation(summary = "更新出行飞机或高铁班次记录")
    @RepeatSubmit
    public AjaxResult update(@Validated @RequestBody ItineraryTicket itineraryTicket) {
        if (StringUtils.isBlank(itineraryTicket.getItineraryTicketId())) {
            return error("参数错误");
        }
        ItineraryTicket queryItineraryTicket = itineraryTicketService.getById(itineraryTicket.getItineraryTicketId());
        if (queryItineraryTicket == null) {
            return error("出行飞机或高铁班次记录不存在");
        }

        return toAjax(itineraryTicketService.updateById(itineraryTicket));
    }

    /**
     * 删除出行飞机或高铁班次记录
     */
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:delete')")
    @Log(title = "删除出行飞机或高铁班次记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Operation(summary = "删除出行飞机或高铁班次记录")
    @RepeatSubmit
    public AjaxResult delete(@PathVariable(value = "id", required = true) String id) {
        if (StringUtils.isBlank(id)) {
            return error("参数错误");
        }
        ItineraryTicket queryItineraryTicket = itineraryTicketService.getById(id);
        if (queryItineraryTicket == null) {
            return error("出行飞机或高铁班次记录不存在");
        }

        return toAjax(itineraryTicketService.removeById(id));
    }

    /**
     * 查询出行飞机或高铁班次记录列表
     */
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:list')")
    @GetMapping("/list")
    @Operation(summary = "查询出行飞机或高铁班次记录列表")
    public TableDataInfo list(ItineraryTicketParams itineraryTicketParams) {
        startPage();
        List<ItineraryTicketVO> list = itineraryTicketService.selectItineraryTicketVOList(itineraryTicketParams);
        return getDataTable(list);
    }

    /**
     * 查看出行飞机或高铁班次记录详情
     */
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:get')")
    @GetMapping("/{itineraryTicketId}")
    @Operation(summary = "查看出行飞机或高铁班次记录详情")
    public AjaxResult get(@PathVariable("itineraryTicketId") String itineraryTicketId) {
        return AjaxResult.success(itineraryTicketService.getById(itineraryTicketId));
    }

    @Operation(summary = "下载预订飞机或高铁班次导入模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String[] fields = {"personName", "certType", "certNum", "gender", "mobilePhone", "email", "companyName", "companyProp",
                "travelMode", "departureStation", "arrivalStation", "flightTrainNum", "departureDt", "arrivalDt",
                "travelModeBack",  "departureStationBack", "arrivalStationBack", "flightTrainNumBack", "departureDtBack", "arrivalDtBack"};
        ExcelUtil<ItineraryTicketImportParams> util = new ExcelUtil<>(ItineraryTicketImportParams.class);
        util.showColumn(fields);
        util.importTemplateInit(response, "预订飞机或高铁班次导入模版");
        util.setDropdownValidation(getDictLabelArr("ei_cert_type_code"), 2, 1, 3000);
        util.setDropdownValidation(getDictLabelArr("ei_travel_mode"), 8, 1, 3000);
        util.setDropdownValidation(getDictLabelArr("ei_travel_mode"), 14, 1, 3000);
        util.exportExcel(response);
    }

    @Log(title = "活动预订飞机或高铁班次信息导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:import')")
    @PostMapping("/importData/{activityId}")
    @Operation(summary = "活动预订飞机或高铁班次信息导入")
    public AjaxResult importData(@PathVariable(value = "activityId", required = true) String activityId,
                                 @RequestParam(value = "file", required = true) MultipartFile file) throws Exception {
        return itineraryTicketService.saveBatchImport(activityId, file);
    }

    @Log(title = "导出飞机或高铁班次信息", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ei:itineraryTicket:export')")
    @PostMapping("/export")
    @Operation(summary = "活动预订飞机或高铁班次信息导出")
    public void export(HttpServletResponse response, ItineraryTicketParams itineraryTicketParams) {
        logger.debug("export itineraryTicketParams:{}", JSON.toJSONString(itineraryTicketParams));
        List<ItineraryTicketVO> list = itineraryTicketService.selectItineraryTicketVOList(itineraryTicketParams);
        logger.debug("export ticketList:{}", JSON.toJSONString(list));
        ExcelUtil<ItineraryTicketVO> util = new ExcelUtil<ItineraryTicketVO>(ItineraryTicketVO.class);
        util.exportExcel(response, list, "导出飞机或高铁班次信息");
    }

}
