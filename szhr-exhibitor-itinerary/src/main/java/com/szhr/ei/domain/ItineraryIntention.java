package com.szhr.ei.domain;

import java.time.LocalTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 登记行程飞机高铁意向对象 itinerary_intention
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@TableName(value = "itinerary_intention", autoResultMap = true)
public class ItineraryIntention implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** 登记行程飞机高铁意向表编号 */
    @TableId(value = "itinerary_intention_id" , type = IdType.INPUT)
    private String itineraryIntentionId;

    /** 活动编号 */
    @Excel(name = "活动编号" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 参会单位编号 */
    @Excel(name = "参会单位编号" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 参会用户编号 */
    @Excel(name = "参会用户编号" )
    @TableField(value = "fair_user_id")
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    @NotBlank(message = "旅途方向不能为空")
    @Excel(name = "旅途方向，可选值：GOING, RETURN" )
    @TableField(value = "ticket_direction")
    private String ticketDirection;

    /** 出行方式，飞机、火车、自驾 */
    @Excel(name = "出行方式，飞机、火车、自驾" )
    @TableField(value = "travel_mode")
    private String travelMode;

    /** 客户选择出发日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "客户选择出发日期", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "departure_date")
    private Date departureDate;

    /** 客户选择出发时间段开始时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段开始时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    @TableField(value = "start_time")
    private LocalTime startTime;

    /** 客户选择出发时间段结束时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段结束时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    @TableField(value = "end_time")
    private LocalTime endTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 预订状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "预订状态，可选值：0=初始, 1=确认, -1=取消" )
    @TableField(value = "status")
    private Integer status;

}
