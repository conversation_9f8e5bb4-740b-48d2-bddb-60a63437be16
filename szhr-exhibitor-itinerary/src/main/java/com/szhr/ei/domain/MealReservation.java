package com.szhr.ei.domain;

import java.time.LocalDate;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 用户订餐记录对象 meal_reservation
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@TableName(value = "meal_reservation", autoResultMap = true)
public class MealReservation implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "meal_reservation_id" , type = IdType.INPUT)
    private String mealReservationId;

    /** 关联活动编号 */
    @NotBlank(message = "关联活动编号不能为空")
    @Excel(name = "关联活动编号" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 单位编号 */
    @NotBlank(message = "单位编号不能为空")
    @Excel(name = "单位编号" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 用户编号 */
    @NotBlank(message = "用户编号不能为空")
    @Excel(name = "用户编号" )
    @TableField(value = "fair_user_id")
    private String fairUserId;

    /** 用餐日期 */
    @NotNull(message = "用餐日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "meal_date")
    private LocalDate mealDate;

    /** 餐饮类型（1=午餐盒饭，2=集体就餐） */
    @NotNull(message = "餐饮类型不能为空")
    @Excel(name = "餐饮类型")
    @TableField(value = "meal_type")
    private Integer mealType;

    /** 餐饮选项ID */
    @Excel(name = "餐饮选项ID" )
    @TableField(value = "meal_option_id")
    private String mealOptionId;

    /** 午饭盒饭编号 */
    @Excel(name = "午饭盒饭编号" )
    @TableField(value = "meal_box_id")
    private String mealBoxId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 展位号 */
    @Excel(name = "展位号" )
    @TableField(value = "booth_code")
    private String boothCode;

    /** 是否自行解决 */
    @Excel(name = "是否自行解决" )
    @TableField(value = "is_self_help")
    private Integer isSelfHelp;

    /** 预订状态：0-待确认 1-已确认 -1-已取消 */
    @Excel(name = "预订状态：0-待确认 1-已确认 -1-已取消" )
    @TableField(value = "status")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注" )
    @TableField(value = "remarks")
    private String remarks;


}
