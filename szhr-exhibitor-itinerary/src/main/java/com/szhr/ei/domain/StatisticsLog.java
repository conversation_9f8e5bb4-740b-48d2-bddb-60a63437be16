package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 数据统计快照对象 statistics_log
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "statistics_log", autoResultMap = true)
public class StatisticsLog implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 活动ID */
    @Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 用户类型（1-国企，2-私企，3-学生，4-工作人员） */
    @Excel(name = "用户类型" , readConverterExp = "1=-国企，2-私企，3-学生，4-工作人员" )
    @TableField(value = "user_type")
    private Integer userType;

    /** 报名人数 */
    @Excel(name = "报名人数" )
    @TableField(value = "signup_count")
    private Integer signupCount;

    /** 订餐人数 */
    @Excel(name = "订餐人数" )
    @TableField(value = "meal_count")
    private Integer mealCount;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "create_dt")
    private Date createDt;

}
