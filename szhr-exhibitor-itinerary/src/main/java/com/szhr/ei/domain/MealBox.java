package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.EncodeUrlHandler;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 午饭盒饭选项对象 meal_box
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@TableName(value = "meal_box", autoResultMap = true)
public class MealBox implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** 盒饭编号， UUID主键 */
    @TableId(value = "meal_box_id" , type = IdType.INPUT)
    private String mealBoxId;

    /** 餐饮套餐信息编号 */
    @NotBlank(message = "餐饮套餐信息编号不能为空")
    @Excel(name = "餐饮套餐信息编号" )
    @TableField(value = "meal_option_id")
    private String mealOptionId;

    /** 盒饭名称 */
    @NotBlank(message = "盒饭名称不能为空")
    @Excel(name = "盒饭名称" )
    @TableField(value = "box_name")
    private String boxName;

    /** 盒饭描述 */
    @Excel(name = "盒饭描述" )
    @TableField(value = "description")
    private String description;

    /** 盒饭图片 */
    @Excel(name = "盒饭图片" )
    @TableField(value = "photo_url", typeHandler = EncodeUrlHandler.class)
    private String photoUrl;

    /** 排序号 */
    @NotNull(message = "排序号不能为空")
    @Excel(name = "排序号" )
    @TableField(value = "ranking")
    private Integer ranking;

}
