package com.szhr.ei.domain;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.EncodeUrlHandler;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 餐饮套餐信息对象 meal_option
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@TableName(value = "meal_option", autoResultMap = true)
public class MealOption implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "meal_option_id" , type = IdType.INPUT)
    private String mealOptionId;

    /** 关联活动ID */
    @NotBlank(message = "关联活动编号不能为空")
    @Excel(name = "关联活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 餐饮类型（1=午餐盒饭,2=集体就餐） */
    @NotNull(message = "餐饮类型不能为空")
    @Excel(name = "餐饮类型")
    @TableField(value = "meal_type")
    private Integer mealType;

    /** 餐厅名称 */
    @Excel(name = "餐厅名称" )
    @TableField(value = "restaurant_name")
    private String restaurantName;

    /** 餐厅地址 */
    @Excel(name = "餐厅地址" )
    @TableField(value = "address")
    private String address;

    /** 用餐日期 */
    @NotNull(message = "用餐日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "meal_date")
    private LocalDate mealDate;

    /** 就餐时间 */
    @NotNull(message = "就餐时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "就餐时间", width = 30, dateFormat = "HH:mm:ss" )
    @TableField(value = "meal_time")
    private LocalTime mealTime;

    /** 餐厅图片URL */
    @Excel(name = "餐厅图片URL" )
    @TableField(value = "photo_url", typeHandler = EncodeUrlHandler.class)
    private String photoUrl;

    /** 最大用餐人数 */
    @Excel(name = "最大用餐人数" )
    @TableField(value = "max_people")
    private Integer maxPeople;

    /** 用户端选餐开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "用户端选餐开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "selection_start_time")
    private Date selectionStartTime;

    /** 用户端选餐结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "用户端选餐结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "selection_end_time")
    private Date selectionEndTime;

    /** 选项状态：0=待确认 1=已确认 -1=已取消 */
    @Excel(name = "选项状态：0=待确认 1=已确认 -1=已取消" )
    @TableField(value = "status")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

}
