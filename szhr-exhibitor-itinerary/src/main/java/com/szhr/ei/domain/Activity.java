package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.handler.mybatis.EncodeUrlHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 招展行程活动对象 activity
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "activity", autoResultMap = true)
public class Activity implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** 招展行程活动ID */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 活动名称 */
    @Excel(name = "活动名称" )
    @TableField(value = "activity_name")
    private String activityName;

    /** 主办单位 */
    @Excel(name = "主办单位" )
    @TableField(value = "sponsor")
    private String sponsor;

    /** 承办单位 */
    @Excel(name = "承办单位" )
    @TableField(value = "undertaker")
    private String undertaker;

    /** 地点 */
    @Excel(name = "地点" )
    @TableField(value = "fair_address")
    private String fairAddress;

    /** 活动报名开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "活动报名开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm" )
    @TableField(value = "apply_start_date")
    private Date applyStartDate;

    /** 活动报名结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "活动报名结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm" )
    @TableField(value = "apply_end_date")
    private Date applyEndDate;

    /** 活动开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm" )
    @TableField(value = "start_date")
    private Date startDate;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm" )
    @TableField(value = "end_date")
    private Date endDate;

    /** 其它活动关联ID */
    @Excel(name = "其它活动关联ID" )
    @TableField(value = "union_id")
    private String unionId;

    /** logo地址 */
    @Excel(name = "logo地址" )
    @TableField(value = "logo_link", typeHandler = EncodeUrlHandler.class)
    private String logoLink;

    /** 负责人联系电话 */
    @Excel(name = "负责人联系电话" )
    @TableField(value = "manager_phone", typeHandler = AESTypeHandler.class)
    private String managerPhone;

    /** 创建人 */
    @Excel(name = "创建人" )
    @TableField(value = "created_by")
    private Long createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 最后修改人ID */
    @Excel(name = "最后修改人ID" )
    @TableField(value = "last_updated_by")
    private Long lastUpdatedBy;

    /** 最后更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "last_update_dt")
    private Date lastUpdateDt;

    /** 删除人 */
    @JsonIgnore
    @Excel(name = "删除人" )
    @TableField(value = "deleted_by")
    private Long deletedBy;

    /** 删除时间 */
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "delete_dt")
    private Date deleteDt;

}
