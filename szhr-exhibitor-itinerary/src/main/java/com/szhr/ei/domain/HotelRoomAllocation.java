package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 房间分配记录对象 hotel_room_allocation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "hotel_room_allocation", autoResultMap = true)
public class HotelRoomAllocation implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "hotel_room_allocation_id" , type = IdType.INPUT)
    private String hotelRoomAllocationId;

    /** 活动编号 */
    @NotBlank(message = "活动编号不能为空")
    @Excel(name = "活动编号" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 单位编号 */
    @Excel(name = "单位编号" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 酒店编号 */
    @NotBlank(message = "酒店编号不能为空")
    @Excel(name = "酒店编号" )
    @TableField(value = "hotel_id")
    private String hotelId;

    /** 用户编号 */
    @Excel(name = "用户编号" )
    @TableField(value = "fair_user_id")
    private String fairUserId;

    /** 房型编号 */
    @Excel(name = "房型编号" )
    @TableField(value = "room_type_id")
    private String roomTypeId;

    /** 是否与同公司人员同住 */
    @Excel(name = "是否与同公司人员同住" )
    @TableField(value = "group_with_same_company")
    private Integer groupWithSameCompany;

    /** 入住日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "check_in_date")
    private Date checkInDate;

    /** 离开日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "离开日期", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "check_out_date")
    private Date checkOutDate;

    /** 分配状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "分配状态，可选值：0=初始, 1=确认, -1=取消" )
    @TableField(value = "status")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

}
