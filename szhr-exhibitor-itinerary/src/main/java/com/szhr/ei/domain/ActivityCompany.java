package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.DataDictHandler;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 报名单位信息对象 activity_company
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "activity_company", autoResultMap = true)
public class ActivityCompany implements Serializable {
  @Serial
  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /**
   * $column.columnComment
   */
  @TableId(value = "id", type = IdType.INPUT)
  private String id;

  /**
   * 公司名称
   */
  @NotBlank(message = "公司名称不能为空")
  @Excel(name = "公司名称", width = 40, sort = 1)
  @TableField(value = "company_name")
  private String companyName;

  /**
   * 所在行业（多选）
   */
  @Excel(name = "所在行业", handler = DataDictHandler.class, args = "INDUSTRY")
  @TableField(value = "industry")
  private String industry;

  @Excel(name = "单位类型", handler = DataDictHandler.class, args = "COMPANY_TYPE")
  @TableField(value = "company_type")
  private String companyType;

  /**
   * 公司性质
   */
  @Excel(name = "公司性质", width = 40, handler = DataDictHandler.class, args = "COMPANY_PROP")
  @TableField(value = "company_prop")
  private String companyProp;

  /**
   * 公司规模
   */
  @Excel(name = "公司规模", handler = DataDictHandler.class, args = "COMPANY_SCALE")
  @TableField(value = "company_scale")
  private String companyScale;

  /**
   * 联系人
   */
  @Excel(name = "联系人")
  @TableField(value = "contact_person")
  private String contactPerson;

  /**
   * 移动电话
   */
  @NotBlank(message = "移动电话不能为空")
  @Excel(name = "移动电话")
  @TableField(value = "mobile_phone", typeHandler = AESTypeHandler.class)
  private String mobilePhone;

  /**
   * 电子邮箱
   */
  @Excel(name = "电子邮箱")
  @TableField(value = "email", typeHandler = AESTypeHandler.class)
  private String email;

  /**
   * 创建人
   */
  //@Excel(name = "创建人" )
  @TableField(value = "created_by")
  private Long createdBy;

  /**
   * 创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "create_dt")
  private Date createDt;

  /**
   * 最后修改人ID
   */
  //@Excel(name = "最后修改人ID" )
  @TableField(value = "last_updated_by")
  private Long lastUpdatedBy;

  /**
   * 最后更新日期
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "last_update_dt")
  private Date lastUpdateDt;

  /**
   * 删除时间
   */
  @JsonIgnore
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  //@Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
  @TableField(value = "delete_dt")
  private Date deleteDt;

  /**
   * 删除人
   */
  @JsonIgnore
  //@Excel(name = "删除人" )
  @TableField(value = "deleted_by")
  private Long deletedBy;

}
