package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.EncodeUrlHandler;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 活动附件对象 activity_attachment
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "activity_attachment", autoResultMap = true)
public class ActivityAttachment implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 活动ID */
    @NotBlank(message = "活动ID不能为空")
    @Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 附件名称 */
    @NotBlank(message = "附件名称不能为空")
    @Excel(name = "附件名称" )
    @TableField(value = "attach_name")
    private String attachName;

    /** 附件类别 */
    @NotBlank(message = "附件类别不能为空")
    @Excel(name = "附件类别" )
    @TableField(value = "attach_category")
    private String attachCategory;

    /** 链接地址 */
    @Excel(name = "链接地址" )
    @TableField(value = "link_url", typeHandler = EncodeUrlHandler.class)
    private String linkUrl;

    /** 创建人 */
    @Excel(name = "创建人" )
    @TableField(value = "created_by")
    private Long createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

}
