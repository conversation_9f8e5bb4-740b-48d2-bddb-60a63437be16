package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 房型信息对象 room_type
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "room_type", autoResultMap = true)
public class RoomType implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 房型名称 */
    @Excel(name = "房型名称" )
    @TableField(value = "name")
    private String name;

    /** 房型描述 */
    @Excel(name = "房型描述" )
    @TableField(value = "description")
    private String description;

}
