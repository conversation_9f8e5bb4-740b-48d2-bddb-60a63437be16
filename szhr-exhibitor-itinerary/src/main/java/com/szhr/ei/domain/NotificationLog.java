package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 通知发送记录对象 notification_log
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "notification_log", autoResultMap = true)
public class NotificationLog implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 用户ID */
    @Excel(name = "用户ID" )
    @TableField(value = "fair_user_id")
    private String fairUserId;

    /** 活动ID */
    @Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 通知类型（1-sms，2-email） */
    @Excel(name = "通知类型" , readConverterExp = "1=-sms，2-email" )
    @TableField(value = "type")
    private Integer type;

    /** 通知内容 */
    @Excel(name = "通知内容" )
    @TableField(value = "content")
    private String content;

    /** 发送状态（1-待发送，2-已发送，3-失败） */
    @Excel(name = "发送状态" , readConverterExp = "1=-待发送，2-已发送，3-失败" )
    @TableField(value = "status")
    private Integer status;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "sent_time")
    private Date sentTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

}
