package com.szhr.ei.domain;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 酒店入住统计信息对象 hotel_daily_stat
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@TableName(value = "hotel_daily_stat", autoResultMap = true)
public class HotelDailyStat implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 酒店编号 */
    @Excel(name = "酒店编号" )
    @TableField(value = "hotel_id")
    private String hotelId;

    /** 关联活动编号 */
    @Excel(name = "关联活动编号" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 房间预定日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "房间预定日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "reserve_date")
    private Date reserveDate;

    /** 酒店目前已入住人数 */
    @Excel(name = "酒店目前已入住人数" )
    @TableField(value = "curr_num")
    private Integer currNum;

    /** 酒店最大入住人数 */
    @Excel(name = "酒店最大入住人数" )
    @TableField(value = "max_capacity")
    private Long maxCapacity;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "update_dt")
    private Date updateDt;

}
