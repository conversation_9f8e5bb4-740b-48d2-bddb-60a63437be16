package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 邀约库对象 activity_invitation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName(value = "activity_invitation", autoResultMap = true)
public class ActivityInvitation implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 活动ID */
    //@Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 活动关联单位ID */
    //@Excel(name = "活动关联单位ID" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 创建人 */
    //@Excel(name = "创建人" )
    @TableField(value = "created_by")
    private Long createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", sort = 22, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 业务人员反馈情况 */
    @Excel(name = "业务人员反馈情况", sort = 11 )
    @TableField(value = "business_feedback")
    private String businessFeedback;

    /** AI反馈情况 */
    @Excel(name = "AI反馈情况", sort = 12 )
    @TableField(value = "ai_feedback")
    private String aiFeedback;

    /** 状态（-1：移除记录，0：初始，1：已外呼） */
    @Excel(name = "状态", sort = 13 , dictType = "ei_invitation_state")
    @TableField(value = "state")
    private Integer state;

    /** 外呼操作人 */
    @Excel(name = "外呼操作人", sort = 14 )
    @TableField(value = "called_by")
    private Long calledBy;

    /** 外呼时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "外呼时间", sort = 14, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "call_dt")
    private Date callDt;

    /** 呼叫结果（0：未接通，1：已接通） */
    @Excel(name = "呼叫结果", sort = 16 , readConverterExp = "0=未接通,1=已接通" )
    @TableField(value = "call_result_number")
    private Integer callResultNumber;

    /** 外呼api响应结果 */
    @Excel(name = "外呼api响应结果", sort = 17 )
    @TableField(value = "api_result")
    private String apiResult;

    /** 管理员ID */
    //@Excel(name = "管理员ID" )
    @TableField(value = "managed_by")
    private Long managedBy;

    /** 最后修改人ID */
    //@Excel(name = "最后修改人ID" )
    @TableField(value = "last_updated_by")
    private Long lastUpdatedBy;

    /** 最后更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    //@Excel(name = "最后更新日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "last_update_dt")
    private Date lastUpdateDt;

    /** 删除人 */
    @JsonIgnore
    //@Excel(name = "删除人" )
    @TableField(value = "deleted_by")
    private Long deletedBy;

    /** 删除时间 */
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    //@Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "delete_dt")
    private Date deleteDt;

    /** 顾客ID */
    @TableField(value = "customer_id")
    private String customerId;
}
