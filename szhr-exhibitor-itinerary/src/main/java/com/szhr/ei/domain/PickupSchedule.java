package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 接送安排对象 pickup_schedule
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@TableName(value = "pickup_schedule", autoResultMap = true)
public class PickupSchedule implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "id" , type = IdType.INPUT)
    private String id;

    /** 活动ID */
    @Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 司机姓名 */
    @Excel(name = "司机姓名" )
    @TableField(value = "driver_name")
    private String driverName;

    /** 司机联系电话 */
    @Excel(name = "司机联系电话" )
    @TableField(value = "driver_phone")
    private String driverPhone;

    /** 车牌号 */
    @Excel(name = "车牌号" )
    @TableField(value = "license_plate")
    private String licensePlate;

    /** 出发地 */
    @Excel(name = "出发地" )
    @TableField(value = "departure_location")
    private String departureLocation;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "departure_time")
    private Date departureTime;

    /** 目的地 */
    @Excel(name = "目的地" )
    @TableField(value = "destination")
    private String destination;

    /** 工作人员姓名 */
    @Excel(name = "工作人员姓名" )
    @TableField(value = "staff_name")
    private String staffName;

    /** 工作人员电话 */
    @Excel(name = "工作人员电话" )
    @TableField(value = "staff_phone")
    private String staffPhone;

}
