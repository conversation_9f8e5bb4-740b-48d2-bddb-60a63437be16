package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 出行飞机或高铁班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@TableName(value = "itinerary_ticket", autoResultMap = true)
public class ItineraryTicket implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** 订票编号 */
    @TableId(value = "itinerary_ticket_id" , type = IdType.INPUT)
    private String itineraryTicketId;

    /** 活动编号 */
    @NotBlank(message = "活动编号不能为空")
    @Excel(name = "活动编号" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 参会单位编号 */
    @NotBlank(message = "参会单位编号不能为空")
    @Excel(name = "参会单位编号" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 参会用户编号 */
    @NotBlank(message = "参会用户编号不能为空")
    @Excel(name = "参会用户编号" )
    @TableField(value = "fair_user_id")
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    @NotBlank(message = "旅途方向不能为空")
    @Excel(name = "旅途方向，可选值：GOING, RETURN" )
    @TableField(value = "ticket_direction")
    private String ticketDirection;

    /** 始发站 */
    @NotBlank(message = "始发站不能为空")
    @Excel(name = "始发站" )
    @TableField(value = "departure_station")
    private String departureStation;

    /** 终点站 */
    @NotBlank(message = "终点站不能为空")
    @Excel(name = "终点站" )
    @TableField(value = "arrival_station")
    private String arrivalStation;

    /** 出行类型，飞机、火车、自驾 */
    @NotBlank(message = "出行类型不能为空")
    @Excel(name = "出行类型，飞机、火车、自驾" )
    @TableField(value = "travel_mode")
    private String travelMode;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "departure_dt")
    private Date departureDt;

    /** 到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "到达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "arrival_dt")
    private Date arrivalDt;

    /** 航班号/车次号 */
    @NotBlank(message = "航班号/车次号不能为空")
    @Excel(name = "航班号/车次号" )
    @TableField(value = "flight_train_num")
    private String flightTrainNum;

    /** 座位号 */
    @Excel(name = "座位号" )
    @TableField(value = "seat_num")
    private String seatNum;

    /** 预定状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "预定状态，可选值：0=初始, 1=确认, -1=取消" )
    @TableField(value = "status")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "update_dt")
    private Date updateDt;

    /** 备注 */
    @Excel(name = "备注" )
    @TableField(value = "remarks")
    private String remarks;

}
