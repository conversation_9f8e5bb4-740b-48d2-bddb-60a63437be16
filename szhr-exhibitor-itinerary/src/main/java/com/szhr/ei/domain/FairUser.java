package com.szhr.ei.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.DataDictHandler;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息对象 fair_user
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@TableName(value = "fair_user", autoResultMap = true)
public class FairUser implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "fair_user_id" , type = IdType.INPUT)
    private String fairUserId;

    /** 姓名 */
    @NotBlank(message = "姓名不能为空")
    @Excel(name = "姓名", sort = 3 )
    @TableField(value = "person_name")
    private String personName;

    /** 性别 */
    @NotBlank(message = "性别不能为空")
    @Excel(name = "性别", sort = 4, handler = DataDictHandler.class, args = "GENDER")
    @TableField(value = "gender")
    private String gender;

    /** 手机号 */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    @Excel(name = "手机号", sort = 5 )
    @TableField(value = "mobile_phone", typeHandler = AESTypeHandler.class)
    private String mobilePhone;

    /** 个人证件类型 */
    @NotBlank(message = "个人证件类型不能为空")
    @Excel(name = "个人证件类型", sort = 6 , dictType = "ei_cert_type_code")
    @TableField(value = "cert_type_code")
    private String certTypeCode;

    /** 个人证件号码 */
    @Excel(name = "个人证件号码", sort = 7 )
    @TableField(value = "cert_num", typeHandler = AESTypeHandler.class)
    private String certNum;

    /** email */
    @Excel(name = "邮箱", sort = 8 )
    @TableField(value = "email", typeHandler = AESTypeHandler.class)
    private String email;

    /** 活动ID */
    //@Excel(name = "活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 关联活动单位编号 */
    //@Excel(name = "关联活动单位编号" )
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 创建人 */
    //@Excel(name = "创建人" )
    @TableField(value = "created_by")
    private Long createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", sort = 8, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 最后修改人ID */
    //@Excel(name = "最后修改人ID" )
    @TableField(value = "last_updated_by")
    private Long lastUpdatedBy;

    /** 最后更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    //@Excel(name = "最后更新日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "last_update_dt")
    private Date lastUpdateDt;

    /** 最后更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    //@Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "last_login_dt")
    private Date lastLoginDt;

    /** 删除时间 */
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    //@Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "delete_dt")
    private Date deleteDt;

    /** 删除人 */
    @JsonIgnore
    //@Excel(name = "删除人" )
    @TableField(value = "deleted_by")
    private Long deletedBy;

}


