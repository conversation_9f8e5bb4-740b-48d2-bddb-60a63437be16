  package com.szhr.ei.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.EncodeUrlHandler;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 酒店信息对象 hotel
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@TableName(value = "hotel", autoResultMap = true)
public class Hotel implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "hotel_id" , type = IdType.INPUT)
    private String hotelId;

    /** 关联活动ID */
    @NotBlank(message = "关联活动ID不能为空")
    @Excel(name = "关联活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 酒店名称 */
    @NotBlank(message = "酒店名称不能为空")
    @Excel(name = "酒店名称" )
    @TableField(value = "name")
    private String name;

    /** 酒店目前已入住人数 */
    @Excel(name = "酒店目前已入住人数" )
    @TableField(value = "curr_num")
    private Integer currNum;

    /** 酒店最大入住人数 */
    @Excel(name = "酒店最大入住人数" )
    @TableField(value = "max_capacity")
    private Integer maxCapacity;

    /** 酒店地址 */
    @NotBlank(message = "酒店地址不能为空")
    @Excel(name = "酒店地址" )
    @TableField(value = "address")
    private String address;

    /** 是否为备用酒店 */
    @Excel(name = "是否为备用酒店" )
    @TableField(value = "backup")
    private Boolean backup;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    @TableField(value = "create_dt")
    private Date createDt;

    /** 前台电话 */
    @Excel(name = "前台电话" )
    @TableField(value = "front_desk_phone")
    private String frontDeskPhone;

    /** 酒店图片 */
    @Excel(name = "酒店图片" )
    @TableField(value = "img_url", typeHandler = EncodeUrlHandler.class)
    private String imgUrl;

}
