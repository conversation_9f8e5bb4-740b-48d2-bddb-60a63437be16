package com.szhr.ei.constant;

import com.szhr.common.utils.DictUtils;

public class ReadConverterExpConstants {
    public static final String COMPANY_TYPE = "0=未知,1=市属国企";

    public static final String COMPANY_PROP = "01=国企,02=上市公司,03=民企,04=外企,05=国家机关,06=事业单位,07=港、澳、台商投资企业（合作、合资、独资）,08=NGO,09=其它";

    public static final String COMPANY_SCALE = "01=50人以下,02=50-100人,03=100-300人,04=300-500人,05=500-1000人,06=1000人以上";

    public static final String INVITATION_STATE = "0=初始,1=已外呼,2=已入库";

    public static final String GENDER = "1=男,2=女";

    public static final String CERT_TYPE_CODE = "1=居民身份证,2=港澳居民来往内地通行证,3=台湾居民来往大陆通行证,4=护照";

    public static final String COMMON_STATUS = "0=初始,1=确认,-1=取消";

    public static final String COMMON_YESORNO = "1=是,0=否";

    public static final String TICKET_DIRECTION = "GOING=去程,RETURN=回程";

    public static final String TRAVEL_MODE = "飞机=飞机,高铁=高铁,自驾=自驾";

    public static final String MEAL_TYPE = "1=午餐盒饭,2=集体就餐";


}


