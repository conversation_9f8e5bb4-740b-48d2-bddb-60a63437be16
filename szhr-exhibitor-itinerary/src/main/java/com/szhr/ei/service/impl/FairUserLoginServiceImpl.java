package com.szhr.ei.service.impl;

import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.common.constant.CacheConstants;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.domain.model.LoginFairUser;
import com.szhr.common.core.redis.RedisCache;
import com.szhr.common.exception.NotNullException;
import com.szhr.common.exception.user.*;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.ip.IpUtils;
import com.szhr.ei.converter.request.FairUserLoginParams;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.mapper.FairUserMapper;
import com.szhr.ei.service.IFairUserLoginService;
import com.szhr.framework.web.service.FrontTokenService;
import com.szhr.system.service.ISysConfigService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FairUserLoginServiceImpl extends ServiceImpl<FairUserMapper, FairUser> implements IFairUserLoginService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private FairUserMapper fairUserMapper;

    @Resource
    private FrontTokenService frontTokenService;

    @Autowired
    private ISysConfigService configService;

    @Resource
    private RedisCache redisCache;

    @Override
    public FairUser exitsMobilePhoneUser(String mobilePhone, String activityId) {
        LambdaQueryWrapper<FairUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairUser::getMobilePhone, AESTypeHandler.encrypt(mobilePhone));
        wrapper.eq(FairUser::getActivityId, activityId);
        wrapper.isNull(FairUser::getDeleteDt);
        return fairUserMapper.selectOne(wrapper);
    }

    /**
     * 登录验证
     *
     * @param fairUserLoginParams
     * @return 结果
     */
    public String fairUserLogin(FairUserLoginParams fairUserLoginParams) {
        String activityId = fairUserLoginParams.getActivityId();
        String mobilePhone = fairUserLoginParams.getMobilePhone();
        String smsCode = fairUserLoginParams.getSmsCode();

        // 验证码校验
        //validateCaptcha(mobilePhone, code, uuid);

        // 登录前置校验
        loginPreCheck(mobilePhone, smsCode);

        // 用户验证
        FairUser fairUser = exitsMobilePhoneUser(mobilePhone, activityId);
        if (fairUser == null) {
            throw new UserNotExistsException();
        }
        recordLoginInfo(fairUser.getFairUserId());

        // 生成token
        LoginFairUser loginFairUser = new LoginFairUser();
        loginFairUser.setActivityId(activityId);
        loginFairUser.setFairUserId(fairUser.getFairUserId());
        loginFairUser.setMobilePhone(fairUser.getMobilePhone());
        return frontTokenService.createToken(loginFairUser);
    }

    /**
     * 校验验证码
     *
     * @param mobilePhone 手机号
     * @param code 图形验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String mobilePhone, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param mobilePhone 用户名
     * @param smsCode 用户密码
     */
    public void loginPreCheck(String mobilePhone, String smsCode) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(mobilePhone) || StringUtils.isEmpty(smsCode)) {
            throw new NotNullException();
        }

        // 用户名不在指定范围内 错误
        if (mobilePhone.length() != 11) {
            throw new UserMobilePhoneNotMatchException();
        }

        // 密码如果不在指定范围内 错误
        if (smsCode.length() != 6) {
            throw new UserMobilePhoneNotMatchException();
        }

        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            throw new BlackListException();
        }

        String verifyKeySms = CacheConstants.MOBILE_PHONE_KEY + mobilePhone;
        String smsSendCodeRedis = redisCache.getCacheObject(verifyKeySms);
        if(StringUtils.isBlank(smsSendCodeRedis)) {
            throw new NotNullException();
        }

        redisCache.deleteObject(verifyKeySms);

        if (!StringUtils.equalsIgnoreCase(smsSendCodeRedis, smsCode)) {
            throw new UserSmsCodeNotMatchException();
        }

    }

    /**
     * 记录登录信息
     *
     * @param fairUserId 前端用户ID
     */
    public void recordLoginInfo(String fairUserId) {
        FairUser fairUser = new FairUser();
        fairUser.setFairUserId(fairUserId);
        //fairUser.setLoginIp(IpUtils.getIpAddr());
        fairUser.setLastLoginDt(DateUtils.getNowDate());
        fairUserMapper.updateById(fairUser);
    }
}
