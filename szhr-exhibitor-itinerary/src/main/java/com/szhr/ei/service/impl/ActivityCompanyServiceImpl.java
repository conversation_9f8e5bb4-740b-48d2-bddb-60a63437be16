package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.ActivityCompanyParams;
import com.szhr.ei.domain.ActivityCompany;
import com.szhr.ei.converter.response.ActivityCompanyVO;
import com.szhr.ei.mapper.ActivityCompanyMapper;
import com.szhr.ei.service.IActivityCompanyService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报名单位信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class ActivityCompanyServiceImpl extends ServiceImpl<ActivityCompanyMapper, ActivityCompany> implements IActivityCompanyService {
    @Resource
    private ActivityCompanyMapper activityCompanyMapper;

    @Override
    public List<ActivityCompanyVO> selectActivityList(ActivityCompanyParams activityCompany) {
        return activityCompanyMapper.selectActivityList(activityCompany);
    }
}
