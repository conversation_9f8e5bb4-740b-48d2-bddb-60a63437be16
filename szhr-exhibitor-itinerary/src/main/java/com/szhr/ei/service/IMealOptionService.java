package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;
import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.domain.Schedule;

/**
 * 餐饮套餐信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMealOptionService extends IService<MealOption> {

    List<MealOption> listByCondition(MealOption mealOption);

    List<MealOptionVO> selectVOList(MealOptionQueryParams mealOptionQueryParams);

    boolean setImgUrlToNull(String id);

    List<MealOption> selectTimeoutNotChoosedMealOptionList();

}
