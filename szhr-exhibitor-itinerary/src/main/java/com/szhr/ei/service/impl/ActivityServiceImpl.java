package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.ActivityParams;
import com.szhr.ei.converter.request.ActivityQueryParams;
import com.szhr.ei.converter.response.ActivityQueryResponseFromUser;
import com.szhr.ei.converter.response.ActivityVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.ActivityMapper;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.service.IActivityService;

import java.util.List;

/**
 * 招展行程活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements IActivityService {
    @Resource
    private ActivityMapper activityMapper;

    @Override
    public List<ActivityVO> selectActivityList(ActivityParams activity) {
        return activityMapper.selectActivityList(activity);
    }

    @Override
    public   List<ActivityQueryResponseFromUser> seclectActivityBasedOnFairUser(ActivityQueryParams activityQueryParams) {
         return activityMapper.seclectActivityBasedOnFairUser(activityQueryParams);
    }

}
