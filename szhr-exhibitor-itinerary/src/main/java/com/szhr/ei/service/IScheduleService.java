package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.request.ScheduleQueryParams;
import com.szhr.ei.converter.response.ScheduleVO;
import com.szhr.ei.domain.Schedule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 日程安排信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IScheduleService extends IService<Schedule> {

    List<ScheduleVO> listByCondition(Schedule schedule);


    List<ScheduleVO> selectScheduleByParams(ScheduleQueryParams scheduleQueryParams);

    ///boolean setImgUrlToNull(String id);
}
