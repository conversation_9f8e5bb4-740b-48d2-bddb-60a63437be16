package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.response.HotelStatVO;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.converter.mapstruct.HotelConvert;
import com.szhr.ei.converter.response.HotelVO;
import com.szhr.ei.mapper.ActivityMapper;
import com.szhr.ei.mapper.HotelMapper;
import com.szhr.ei.service.IHotelService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 酒店信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class HotelServiceImpl extends ServiceImpl<HotelMapper, Hotel> implements IHotelService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private HotelMapper hotelMapper;
    @Resource
    private ActivityMapper activityMapper;
    @Autowired
    private HotelConvert hotelConvert;

    @Override
    public List<HotelVO> listByCondition(Hotel hotel) {
        List<HotelVO> listHotelVO = new ArrayList<>();
        try {
            LambdaQueryWrapper<Hotel> wrapper = new LambdaQueryWrapper<>();
            if (!StringUtils.isBlank(hotel.getActivityId())) {
                wrapper.eq(Hotel::getActivityId, hotel.getActivityId());
            }
            if (StringUtils.isNotBlank(hotel.getName())) {
                wrapper.like(Hotel::getName, hotel.getName());
            }

            List<Hotel> list = hotelMapper.selectList(wrapper);

            for (Hotel item : list) {
                HotelVO hotelVO = hotelConvert.toVo(item);
                if (StringUtils.isNotBlank(item.getActivityId())) {
                    Activity activity = activityMapper.selectById(item.getActivityId());
                    if (activity != null) {
                        hotelVO.setActivityName(activity.getActivityName());
                    }
                }
                listHotelVO.add(hotelVO);

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return listHotelVO;
    }

    @Override
    public boolean setImgUrlToNull(String id) {
        LambdaUpdateWrapper<Hotel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Hotel::getHotelId, id).set(Hotel::getImgUrl, null);  // 设置为 NULL
        int updateRow = hotelMapper.update(null, wrapper);
        if (updateRow > 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<HotelStatVO> queryHotelStatVOList(Hotel hotel) {
        return hotelMapper.queryHotelStatVOList(hotel);
    }
}
