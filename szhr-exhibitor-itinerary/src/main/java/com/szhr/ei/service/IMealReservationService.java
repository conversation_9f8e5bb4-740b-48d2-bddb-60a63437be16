package com.szhr.ei.service;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.dto.MealReservationDTO;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.request.MealReservationQueryParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.converter.response.MealReservationVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.domain.MealReservation;

/**
 * 用户订餐记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMealReservationService extends IService<MealReservation> {

    List<MealReservation> listByCondition(MealReservation mealReservation);

    List<MealOptionVO> getAvailableMealOptions(String userId, Integer mealType);

    MealReservationVO getUserCurentSelection(String userId, String activityId, Integer mealType, Date mealDate);

    List<MealReservationVO> selectMealReservationVOList(MealReservationQueryParams mealReservationQueryParams);

    List<FairUser> getNotChoosedFairUserList(MealOption mealOption);

    void batchInsertTimeoutSelfHelp(MealOption mealOption, List<FairUser> userList);

    List<MealReservationDTO> selectTimeoutNotChoosedMealReservationList(MealReservationDTO mealReservationDTO);

    void batchInsertTimeoutSelfHelp(List<MealReservationDTO> dtoList);
}
