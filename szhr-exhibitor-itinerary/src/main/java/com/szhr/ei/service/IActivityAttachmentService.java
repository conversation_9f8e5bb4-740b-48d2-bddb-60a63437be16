package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.domain.ActivityAttachment;

import java.util.List;

/**
 * 活动附件Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IActivityAttachmentService extends IService<ActivityAttachment> {
    /**
     * 根据ID列表查询linkUrl列表
     * @param idList 用户ID列表
     * @return linkUrl列表
     */
    List<String> getLinkUrlListByIds(List<String> idList);

    List<ActivityAttachment> selectActivityAttachmentList(ActivityAttachment activityAttachment);
}
