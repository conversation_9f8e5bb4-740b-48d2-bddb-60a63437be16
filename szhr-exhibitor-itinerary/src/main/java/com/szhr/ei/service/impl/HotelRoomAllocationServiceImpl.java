package com.szhr.ei.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.mapstruct.HotelRoomAllocationConvert;
import com.szhr.ei.converter.request.HotelRoomAllocationQueryParams;
import com.szhr.ei.converter.response.HotelRoomAllocationExport;
import com.szhr.ei.converter.response.HotelRoomAllocationResponce;
import com.szhr.ei.converter.response.HotelRoomAllocationVO;
import com.szhr.ei.converter.response.HotelRoomSmsMail;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.domain.Schedule;
import com.szhr.ei.mapper.ActivityMapper;
import com.szhr.ei.mapper.HotelMapper;
import com.szhr.ei.mapper.HotelRoomAllocationMapper;
import com.szhr.ei.mapper.ScheduleMapper;
import com.szhr.ei.service.IHotelRoomAllocationService;
import com.szhr.framework.web.service.PushService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.thymeleaf.context.Context;
import org.thymeleaf.TemplateEngine;

/**
 * 房间分配记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class HotelRoomAllocationServiceImpl extends ServiceImpl<HotelRoomAllocationMapper, HotelRoomAllocation> implements IHotelRoomAllocationService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private PushService pushService;

    @Resource
    private HotelRoomAllocationMapper hotelRoomAllocationMapper;

    @Resource
    private HotelRoomAllocationConvert hotelRoomAllocationConvert;

    @Resource
    private HotelMapper hotelMapper;

    @Resource
    private ScheduleMapper scheduleMapper;

    @Autowired
    private TemplateEngine templateEngine;

    @Override
    public List<HotelRoomAllocation> listByCondition(HotelRoomAllocation hotelRoomAllocation) {
        return hotelRoomAllocationMapper.selectList(
                Wrappers.<HotelRoomAllocation>lambdaQuery()
                        .eq(!StringUtils.isBlank(hotelRoomAllocation.getActivityId()), HotelRoomAllocation::getActivityId, hotelRoomAllocation.getActivityId())
                        .eq(StringUtils.isNotBlank(hotelRoomAllocation.getActivityCompanyId()), HotelRoomAllocation::getActivityCompanyId, hotelRoomAllocation.getActivityCompanyId())
                        .eq(StringUtils.isNotBlank(hotelRoomAllocation.getFairUserId()), HotelRoomAllocation::getFairUserId, hotelRoomAllocation.getFairUserId())
                        .eq(StringUtils.isNotBlank(hotelRoomAllocation.getHotelId()), HotelRoomAllocation::getHotelId, hotelRoomAllocation.getHotelId())
                        .eq(StringUtils.isNotBlank(hotelRoomAllocation.getRoomTypeId()), HotelRoomAllocation::getRoomTypeId, hotelRoomAllocation.getRoomTypeId())
                        .eq(Objects.nonNull(hotelRoomAllocation.getStatus()), HotelRoomAllocation::getStatus, hotelRoomAllocation.getStatus()));
    }

    @Override
    public List<HotelRoomAllocationVO> listByCondition(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams) {
        List<HotelRoomAllocationVO> hotelRoomAllocationVOList = new ArrayList<>();
        try {
            LambdaQueryWrapper<HotelRoomAllocation> wrapper = new LambdaQueryWrapper<>();
            if (!StringUtils.isBlank(hotelRoomAllocationQueryParams.getActivityId())) {
                wrapper.eq(HotelRoomAllocation::getActivityId, hotelRoomAllocationQueryParams.getActivityId());
            }

            if (!StringUtils.isBlank(hotelRoomAllocationQueryParams.getHotelId())) {
                wrapper.eq(HotelRoomAllocation::getHotelId, hotelRoomAllocationQueryParams.getHotelId());
            }

            if (StringUtils.isNotBlank(hotelRoomAllocationQueryParams.getActivityCompanyId())) {
                wrapper.like(HotelRoomAllocation::getActivityCompanyId, hotelRoomAllocationQueryParams.getActivityCompanyId());
            }

            List<HotelRoomAllocation> list = hotelRoomAllocationMapper.selectList(wrapper);

            for (HotelRoomAllocation item : list) {
                HotelRoomAllocationVO hotelVO = hotelRoomAllocationConvert.toVo(item);
                if (StringUtils.isNotBlank(item.getActivityId())) {
                    Activity activity = activityMapper.selectById(item.getActivityId());
                    if (activity != null) {
                        hotelVO.setActivityName(activity.getActivityName());
                    }
                }
                hotelRoomAllocationVOList.add(hotelVO);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return hotelRoomAllocationVOList;
    }

    @Override
    public String sendSmsMail(List<String> ids) {
        for (String id : ids) {
            HotelRoomSmsMail hotelRoomSmsMail = hotelRoomAllocationMapper.getHotelRoomSmsMailById(id);

            if(hotelRoomSmsMail != null) {
                List<Schedule> list = scheduleMapper.selectList(new LambdaQueryWrapper<Schedule>().eq(Schedule::getActivityId, hotelRoomSmsMail.getActivityId()));
//                if(StringUtils.isNotBlank(hotelRoomSmsMail.getMobilePhone())) {
//                    // 暂时不发短信
//                    pushService.sendBySMS(hotelRoomSmsMail.getMobilePhone(), "SMS_180955001", null);
//                }
                if(StringUtils.isNotBlank(hotelRoomSmsMail.getEmail())) {
                    pushService.sendMail(hotelRoomSmsMail.getEmail(), "邮件通知", getMailContent(hotelRoomSmsMail, list));
                }
            }
        }
        return "发送完成";
    }

    @Override
    public List<HotelRoomAllocationExport> selectHotelRoomAllocationExportList(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams) {
        return hotelRoomAllocationMapper.selectHotelRoomAllocationExportList(hotelRoomAllocationQueryParams);
    }

    private String getMailContent(HotelRoomSmsMail hotelRoomSmsMail, List<Schedule> schedules){
        Context context = new Context();
        context.setVariable("hotelRoomSmsMail", hotelRoomSmsMail);
        context.setVariable("schedules", schedules);
        // 模板名称对应 resources/templates/sendHotelRoomScheduleMail.html
        return templateEngine.process("sendHotelRoomScheduleMail", context);
    }

    @Override
    public List<HotelRoomAllocationResponce> selectHotelInformationList(HotelRoomAllocation hotelRoomAllocation) {
        return hotelRoomAllocationMapper.selectHotelInformationList(hotelRoomAllocation);
    }
}
