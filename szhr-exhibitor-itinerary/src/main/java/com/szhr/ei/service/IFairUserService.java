package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.request.FairUserParams;
import com.szhr.ei.converter.response.FairUserVO;
import com.szhr.ei.domain.FairUser;

import java.util.List;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IFairUserService extends IService<FairUser> {

    List<FairUserVO> selectFairUserList(FairUserParams fairUserParams);
}
