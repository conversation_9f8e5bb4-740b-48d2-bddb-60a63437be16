package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.mapstruct.ScheduleConvert;
import com.szhr.ei.converter.request.ScheduleQueryParams;
import com.szhr.ei.converter.response.ScheduleVO;
import com.szhr.ei.domain.*;
import com.szhr.ei.mapper.ActivityMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.ScheduleMapper;
import com.szhr.ei.domain.Schedule;
import com.szhr.ei.service.IScheduleService;

import java.util.ArrayList;
import java.util.List;

/**
 * 日程安排信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper, Schedule> implements IScheduleService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ScheduleMapper scheduleMapper;
    @Resource
    private ActivityMapper activityMapper;
    @Autowired
    private ScheduleConvert scheduleConvert;

    @Override
    public List<ScheduleVO> listByCondition(Schedule schedule) {
        List<ScheduleVO> listScheduleVO = new ArrayList<>();
        try {
            LambdaQueryWrapper<Schedule> wrapper = new LambdaQueryWrapper<>();
            if (!StringUtils.isBlank(schedule.getScheduleId())) {
                wrapper.eq(Schedule::getScheduleId, schedule.getScheduleId());
            }else {

                if (!StringUtils.isBlank(schedule.getActivityId())) {
                    wrapper.eq(Schedule::getActivityId, schedule.getActivityId());
                }

                if (schedule.getScheduleDate() != null) {
                    wrapper.eq(Schedule::getScheduleDate, schedule.getScheduleDate());
                }

                if (schedule.getScheduleTime() != null) {
                    wrapper.eq(Schedule::getScheduleTime, schedule.getScheduleTime());
                }

                if (!StringUtils.isBlank(schedule.getDepartureAddress())) {
                    wrapper.eq(Schedule::getDepartureAddress, schedule.getDepartureAddress());
                }

                if (!StringUtils.isBlank(schedule.getArrivalAddress())) {
                    wrapper.eq(Schedule::getArrivalAddress, schedule.getArrivalAddress());
                }

                if (!StringUtils.isBlank(schedule.getDriverName())) {
                    wrapper.eq(Schedule::getDriverName, schedule.getDriverName());
                }

                if (!StringUtils.isBlank(schedule.getDriverNumber())) {
                    wrapper.eq(Schedule::getDriverNumber, schedule.getDriverNumber());
                }

                if (!StringUtils.isBlank(schedule.getStaffName())) {
                    wrapper.eq(Schedule::getStaffName, schedule.getStaffName());
                }

                if (!StringUtils.isBlank(schedule.getStaffNumber())) {
                    wrapper.eq(Schedule::getStaffNumber, schedule.getStaffNumber());
                }

                if (!StringUtils.isBlank(schedule.getCarNumber())) {
                    wrapper.eq(Schedule::getCarNumber, schedule.getCarNumber());
                }
            }
            List<Schedule> list = scheduleMapper.selectList(wrapper);

            for (Schedule item : list) {
                ScheduleVO scheduleVO = scheduleConvert.toVo(item);

                if (StringUtils.isNotBlank(item.getActivityId())) {
                    Activity activity = activityMapper.selectById(item.getActivityId());
                    if (activity != null) {
                        scheduleVO.setActivityName(activity.getActivityName());
                    }
                }
                listScheduleVO.add(scheduleVO);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return listScheduleVO;

    }


    public  List<ScheduleVO> selectScheduleByParams(ScheduleQueryParams scheduleQueryParams){
        List<Schedule> list = scheduleMapper.selectScheduleList(scheduleQueryParams);
        List<ScheduleVO> listScheduleVO = new ArrayList<>();
        logger.debug("scheduleQueryParams: {} ",scheduleQueryParams);

        for (Schedule item : list) {
            ScheduleVO scheduleVO = scheduleConvert.toVo(item);

            logger.debug("scheduleVO {} ", scheduleVO);
            if (StringUtils.isNotBlank(item.getActivityId())) {
                Activity activity = activityMapper.selectById(item.getActivityId());
                logger.info("activity: {} ", activity);
                if (activity != null) {
                    scheduleVO.setActivityName(activity.getActivityName());
                }
            }
            listScheduleVO.add(scheduleVO);
        }

        return listScheduleVO;
    }


//    @Override
//    public boolean setImgUrlToNull(String id) {
//        LambdaUpdateWrapper<Schedule> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(Schedule::getScheduleId, id).set(Schedule::getPhotoUrl, null);  // 设置为 NULL
//        int updateRow = scheduleMapper.update(null, wrapper);
//        if (updateRow > 0) {
//            return true;
//        }
//        return false;
//    }
}
