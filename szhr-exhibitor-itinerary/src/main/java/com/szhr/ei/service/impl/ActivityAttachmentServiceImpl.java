package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.domain.ActivityAttachment;
import com.szhr.ei.mapper.ActivityAttachmentMapper;
import com.szhr.ei.service.IActivityAttachmentService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 活动附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class ActivityAttachmentServiceImpl extends ServiceImpl<ActivityAttachmentMapper, ActivityAttachment> implements IActivityAttachmentService {
    @Resource
    private ActivityAttachmentMapper activityAttachmentMapper;

    @Override
    public List<String> getLinkUrlListByIds(List<String> idList) {
        //根据ID列表查询linkUrl列表
        return lambdaQuery()
                .in(ActivityAttachment::getId, idList)
                .select(ActivityAttachment::getLinkUrl)
                .list()
                .stream()
                .map(ActivityAttachment::getLinkUrl)
                .filter(Objects::nonNull) // 过滤掉null
                .filter(Predicate.not(String::isBlank)) // 过滤掉空白字符串（包括空字符串）
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityAttachment> selectActivityAttachmentList(ActivityAttachment activityAttachment) {
        return activityAttachmentMapper.selectActivityAttachmentList(activityAttachment);
    }

    @Override
    public ActivityAttachment getLastestAttach(ActivityAttachment activityAttachment) {
        ActivityAttachment result = null;
        List<ActivityAttachment> list = new ArrayList<>();
        LambdaQueryWrapper<ActivityAttachment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityAttachment::getActivityId, activityAttachment.getActivityId());
        wrapper.eq(ActivityAttachment::getAttachCategory, "会务手册");
        wrapper.orderByDesc(ActivityAttachment::getCreateDt);
        list = activityAttachmentMapper.selectList(wrapper);
        if(list != null) result = list.get(0);
        return result;
    }
}
