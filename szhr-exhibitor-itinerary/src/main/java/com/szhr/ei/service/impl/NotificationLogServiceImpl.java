package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.NotificationLogMapper;
import com.szhr.ei.domain.NotificationLog;
import com.szhr.ei.service.INotificationLogService;

/**
 * 通知发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class NotificationLogServiceImpl extends ServiceImpl<NotificationLogMapper, NotificationLog> implements INotificationLogService {
    @Resource
    private NotificationLogMapper notificationLogMapper;
}
