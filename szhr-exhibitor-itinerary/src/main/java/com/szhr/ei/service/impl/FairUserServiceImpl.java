package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.FairUserParams;
import com.szhr.ei.converter.response.FairUserVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.mapper.FairUserMapper;
import com.szhr.ei.service.IFairUserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class FairUserServiceImpl extends ServiceImpl<FairUserMapper, FairUser> implements IFairUserService {
    @Resource
    private FairUserMapper fairUserMapper;

    @Override
    public List<FairUserVO> selectFairUserList(FairUserParams fairUserParams) {
        return fairUserMapper.selectFairUserList(fairUserParams);
    }


}
