package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.request.ActivityParams;
import com.szhr.ei.converter.request.ActivityQueryParams;
import com.szhr.ei.converter.response.ActivityQueryResponseFromUser;
import com.szhr.ei.domain.Activity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.response.ActivityVO;

/**
 * 招展行程活动Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IActivityService extends IService<Activity> {
    List<ActivityVO> selectActivityList(ActivityParams activity);

    List<ActivityQueryResponseFromUser> seclectActivityBasedOnFairUser(ActivityQueryParams activityQueryParams);
}

