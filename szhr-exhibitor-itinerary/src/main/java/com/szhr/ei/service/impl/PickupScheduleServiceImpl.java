package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.domain.PickupSchedule;
import com.szhr.ei.mapper.PickupScheduleMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.PickupScheduleMapper;
import com.szhr.ei.domain.PickupSchedule;
import com.szhr.ei.service.IPickupScheduleService;

import java.util.List;

/**
 * 接送安排Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class PickupScheduleServiceImpl extends ServiceImpl<PickupScheduleMapper, PickupSchedule> implements IPickupScheduleService {
    @Resource
    private PickupScheduleMapper pickupScheduleMapper;

    @Override
    public List<PickupSchedule> listByCondition(PickupSchedule pickupSchedule) {
        LambdaQueryWrapper<PickupSchedule> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isBlank(pickupSchedule.getId())) {
            wrapper.eq(PickupSchedule::getId, pickupSchedule.getId());
        }

        if (!StringUtils.isBlank(pickupSchedule.getActivityId())) {
            wrapper.eq(PickupSchedule::getActivityId, pickupSchedule.getActivityId());
        }

        if (!StringUtils.isBlank(pickupSchedule.getDriverName())) {
            wrapper.eq(PickupSchedule::getDriverName, pickupSchedule.getDriverName());
        }

        if (!StringUtils.isBlank(pickupSchedule.getDriverPhone())) {
            wrapper.like(PickupSchedule::getDriverPhone, pickupSchedule.getDriverPhone());
        }

        return pickupScheduleMapper.selectList(wrapper);

    }
}
