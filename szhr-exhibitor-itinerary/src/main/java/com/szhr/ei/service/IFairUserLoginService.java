package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.request.FairUserLoginParams;
import com.szhr.ei.domain.FairUser;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IFairUserLoginService extends IService<FairUser> {

    FairUser exitsMobilePhoneUser(String mobilePhone, String activityId);

    String fairUserLogin(FairUserLoginParams fairUserLoginParams);

}
