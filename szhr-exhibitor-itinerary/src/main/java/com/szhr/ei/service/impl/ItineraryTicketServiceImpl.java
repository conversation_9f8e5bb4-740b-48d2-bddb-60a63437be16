package com.szhr.ei.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.common.utils.poi.ExcelUtil;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.mapstruct.ItineraryTicketConvert;
import com.szhr.ei.converter.request.ItineraryTicketImportParams;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.ItineraryTicket;
import com.szhr.ei.mapper.FairUserMapper;
import com.szhr.ei.mapper.ItineraryTicketMapper;
import com.szhr.ei.service.IItineraryTicketService;
import jakarta.annotation.Resource;
import org.apache.ibatis.executor.BatchResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 出行航班或火车班次记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class ItineraryTicketServiceImpl extends ServiceImpl<ItineraryTicketMapper, ItineraryTicket> implements IItineraryTicketService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ItineraryTicketMapper itineraryTicketMapper;
    @Resource
    private FairUserMapper fairUserMapper;
    @Resource
    private ItineraryTicketConvert itineraryTicketConvert;

    @Override
    public List<ItineraryTicketVO> selectItineraryTicketVOList(ItineraryTicketParams itineraryTicketParams) {
        return itineraryTicketMapper.selectItineraryTicketVOList(itineraryTicketParams);
    }

    @Override
    public AjaxResult saveBatchImport(String activityId, MultipartFile file) throws IOException {
        ExcelUtil<ItineraryTicketImportParams> util = new ExcelUtil<ItineraryTicketImportParams>(ItineraryTicketImportParams.class);
        List<ItineraryTicketImportParams> ticketList = util.importExcel(file.getInputStream());
        //logger.debug("import activityId:{}, ticketList: {}", activityId, JSON.toJSONString(ticketList));
        if (CollUtil.isEmpty(ticketList)) {
            return AjaxResult.error("导入数据不能为空！");
        }

        //logger.debug("========stopWatch start:");
        //StopWatch stopWatch = new StopWatch();
        //stopWatch.start();

        //取出ticketList中的有重复的certNum及mobilePhone
        List<String> duplicateCertNums = findDuplicateCertNums(ticketList);
        List<String> duplicateMobilePhones = findDuplicateMobilePhones(ticketList);
        String errorMsg = "";
        if (!duplicateCertNums.isEmpty()) {
            errorMsg += "表格中以下证件号码有重复：" + StringUtils.join(duplicateCertNums, ",");
            errorMsg += "。";
        }
        if (!duplicateMobilePhones.isEmpty()) {
            errorMsg += "表格中以下手机号码有重复：" + StringUtils.join(duplicateMobilePhones, ",");
            errorMsg += "。";
        }

        if (!StringUtils.isEmpty(errorMsg)) {
            return AjaxResult.error(errorMsg);
        }

        LambdaQueryWrapper<FairUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairUser::getActivityId, activityId);
        List<FairUser> activityUserList = fairUserMapper.selectList(wrapper);
        if (CollUtil.isEmpty(activityUserList)) {
            return AjaxResult.error("未查询到导入表中活动用户信息");
        }

        List<String> strIdList = new ArrayList<>();
        List<ItineraryTicket> itineraryTicketList = new ArrayList<>();
        List<ItineraryTicket> itineraryTicketBackList = new ArrayList<>();
        Date nowDate = DateUtils.getNowDate();
        for (ItineraryTicketImportParams ticket : ticketList) {
            //String dictCode = IdCardType.getCodeByDictNameFast(ticket.getCertType());
            for (FairUser user : activityUserList) {
                //logger.debug("========requestCertNum：{}, databaseCertNum: {}========", ticket.getCertNum(), user.getCertNum());
                //logger.debug("========requestMobilePhone：{}, databaseMobilePhone: {}========", ticket.getMobilePhone(), user.getMobilePhone());
                //if (StringUtils.equals(dictCode, user.getCertTypeCode()) && StringUtils.equals(ticket.getCertNum(), user.getCertNum())) {
                if (StringUtils.equals(ticket.getMobilePhone(), user.getMobilePhone())) {
                    strIdList.add(user.getFairUserId());
                    ItineraryTicket itineraryTicket = itineraryTicketConvert.toPo(ticket);
                    if (itineraryTicket != null) {
                        itineraryTicket.setItineraryTicketId(IdUtils.randomUUID());
                        itineraryTicket.setCreateDt(nowDate);
                        itineraryTicket.setUpdateDt(nowDate);
                        itineraryTicket.setRemarks(DictOptionConstans.TICKET_REMARK);
                        itineraryTicket.setActivityId(activityId);
                        itineraryTicket.setFairUserId(user.getFairUserId());
                        itineraryTicket.setActivityCompanyId(user.getActivityCompanyId());
                        itineraryTicket.setTicketDirection(DictOptionConstans.TICKET_DIRECTION_GOING);
                        itineraryTicket.setStatus(DictOptionConstans.COMMON_STATUS_CONFIRM);
                        itineraryTicketList.add(itineraryTicket);
                    }

                    ItineraryTicket itineraryTicketBack = itineraryTicketConvert.toPoBack(ticket);
                    if (itineraryTicketBack != null) {
                        itineraryTicketBack.setItineraryTicketId(IdUtils.randomUUID());
                        itineraryTicketBack.setCreateDt(nowDate);
                        itineraryTicketBack.setUpdateDt(nowDate);
                        itineraryTicketBack.setRemarks(DictOptionConstans.TICKET_REMARK);
                        itineraryTicketBack.setActivityId(activityId);
                        itineraryTicketBack.setFairUserId(user.getFairUserId());
                        itineraryTicketBack.setActivityCompanyId(user.getActivityCompanyId());
                        itineraryTicketBack.setTicketDirection(DictOptionConstans.TICKET_DIRECTION_RETURN);
                        itineraryTicketBack.setStatus(DictOptionConstans.COMMON_STATUS_CONFIRM);
                        itineraryTicketBackList.add(itineraryTicketBack);
                    }
                }
            }
        }

        //logger.debug("========strIdList：{} ========", strIdList.size());

        // 如有：删除旧的绑定活动行程数据
        if (CollUtil.isNotEmpty(strIdList)) {
            int goingCnt = 0, returnCnt = 0;
            if (CollUtil.isNotEmpty(itineraryTicketList)) {
                LambdaQueryWrapper<ItineraryTicket> ticketWrapper = new LambdaQueryWrapper<>();
                ticketWrapper.eq(ItineraryTicket::getActivityId, activityId);
                ticketWrapper.in(ItineraryTicket::getFairUserId, strIdList);
                ticketWrapper.eq(ItineraryTicket::getTicketDirection, DictOptionConstans.TICKET_DIRECTION_GOING);
                List<ItineraryTicket> existTicketList = itineraryTicketMapper.selectList(ticketWrapper);
                if (CollUtil.isNotEmpty(existTicketList)) {
                    List<String> idList = getItineraryTicketIds(existTicketList);
                    itineraryTicketMapper.deleteByIds(idList);
                }

                //logger.debug("import new itineraryTicketList: {}", JSON.toJSONString(itineraryTicketList));
                List<BatchResult> batchResultlist = itineraryTicketMapper.insert(itineraryTicketList);
                if (!CollectionUtils.isEmpty(batchResultlist)) {
                    goingCnt = batchResultlist.size();
                }
            }


            if (CollUtil.isNotEmpty(itineraryTicketBackList)) {
                LambdaQueryWrapper<ItineraryTicket> ticketWrapperBack = new LambdaQueryWrapper<>();
                ticketWrapperBack.eq(ItineraryTicket::getActivityId, activityId);
                ticketWrapperBack.in(ItineraryTicket::getFairUserId, strIdList);
                ticketWrapperBack.eq(ItineraryTicket::getTicketDirection, "RETURN");
                List<ItineraryTicket> existTicketBackList = itineraryTicketMapper.selectList(ticketWrapperBack);
                if (CollUtil.isNotEmpty(existTicketBackList)) {
                    List<String> idList = getItineraryTicketIds(existTicketBackList);
                    itineraryTicketMapper.deleteByIds(idList);
                }

                //logger.debug("import new itineraryTicketBackList: {}", JSON.toJSONString(itineraryTicketBackList));
                List<BatchResult> batchBackResultlist = itineraryTicketMapper.insert(itineraryTicketBackList);
                if (!CollectionUtils.isEmpty(batchBackResultlist)) {
                    returnCnt = batchBackResultlist.size();
                }
            }

            //stopWatch.stop();
            //logger.debug("========stopWatch: {}", stopWatch.getTotalTimeMillis());
            return AjaxResult.success("导入预订人员信息成功，去程：" + goingCnt + "条，回程：" + returnCnt + "条");

        }

        return AjaxResult.error("导入预订人员信息失败");

    }

    private List<String> getItineraryTicketIds(List<ItineraryTicket> ticketList) {
        return ticketList.stream()
                .map(ItineraryTicket::getItineraryTicketId)
                .collect(Collectors.toList());
    }

    private List<String> findDuplicateCertNums(List<ItineraryTicketImportParams> ticketList) {
        // 按 certNum 分组，统计出现次数 > 1 的 certNum
        return ticketList.stream()
                .collect(Collectors.groupingBy(
                        ItineraryTicketImportParams::getCertNum,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 只保留重复的 certNum
                .map(Map.Entry::getKey)                 // 提取 certNum
                .collect(Collectors.toList());
    }

    private List<String> findDuplicateMobilePhones(List<ItineraryTicketImportParams> ticketList) {
        // 按 mobilePhone 分组，统计出现次数 > 1 的 mobilePhone
        return ticketList.stream()
                .collect(Collectors.groupingBy(
                        ItineraryTicketImportParams::getMobilePhone,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 只保留重复的 mobilePhone
                .map(Map.Entry::getKey)                 // 提取 mobilePhone
                .collect(Collectors.toList());
    }
}
