package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.request.ActivityInvitationParams;
import com.szhr.ei.converter.response.ActivityInvitationCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationOptionalCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationVO;
import com.szhr.ei.domain.ActivityInvitation;

import java.util.List;

/**
 * 邀约库Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IActivityInvitationService extends IService<ActivityInvitation> {
    List<ActivityInvitationVO> selectActivityInvitationList(ActivityInvitationParams activityInvitation);

    List<ActivityInvitationCompanyVO> listCompany(ActivityInvitationParams activityInvitation);

    List<ActivityInvitationOptionalCompanyVO> listOptionalCompany(ActivityInvitationParams activityInvitation);
}
