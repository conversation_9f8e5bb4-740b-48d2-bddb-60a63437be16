package com.szhr.ei.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.common.constant.DictOptionConstans;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.converter.dto.MealReservationDTO;
import com.szhr.ei.converter.mapstruct.MealOptionConvert;
import com.szhr.ei.converter.mapstruct.MealReservationConvert;
import com.szhr.ei.converter.request.MealReservationQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.converter.response.MealReservationVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.MealBox;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.domain.MealReservation;
import com.szhr.ei.mapper.MealBoxMapper;
import com.szhr.ei.mapper.MealReservationMapper;
import com.szhr.ei.service.IMealOptionService;
import com.szhr.ei.service.IMealReservationService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户订餐记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MealReservationServiceImpl extends ServiceImpl<MealReservationMapper, MealReservation> implements IMealReservationService {
    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MealReservationMapper mealReservationMapper;

    @Resource
    private MealBoxMapper mealBoxMapper;

    @Override
    public List<MealReservation> listByCondition(MealReservation mealReservation) {
        return mealReservationMapper.selectList(new LambdaQueryWrapper<MealReservation>()
                .eq(StringUtils.isNotBlank(mealReservation.getMealReservationId()), MealReservation::getMealReservationId, mealReservation.getMealReservationId())
                .eq(StringUtils.isNotBlank(mealReservation.getActivityId()), MealReservation::getActivityId, mealReservation.getActivityId())
                .eq(StringUtils.isNotBlank(mealReservation.getFairUserId()), MealReservation::getFairUserId, mealReservation.getFairUserId())
                .like(StringUtils.isNotBlank(mealReservation.getMealOptionId()), MealReservation::getMealOptionId, mealReservation.getMealOptionId())
                .eq(mealReservation.getMealType() != null, MealReservation::getMealType, mealReservation.getMealType())
                .apply(Objects.nonNull(mealReservation.getMealDate()),
                        "DATE_FORMAT(meal_date, '%Y-%m-%d') = DATE_FORMAT({0}, '%Y-%m-%d')", mealReservation.getMealDate())
                .eq(mealReservation.getIsSelfHelp() != null, MealReservation::getIsSelfHelp, mealReservation.getIsSelfHelp()));
    }

    /**
     * 获取用户可选餐饮套餐
     */
    @Resource
    private IMealOptionService mealOptionService;
    private MealOptionConvert mealOptionConvert;

    public List<MealOptionVO> getAvailableMealOptions(String userId, Integer mealType){
        Date now = new Date();
        LambdaQueryWrapper<MealOption> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MealOption::getMealType, mealType)
                .le(MealOption::getSelectionStartTime, now)
                .ge(MealOption::getSelectionEndTime, now);

        List<MealOption> options = mealOptionService.list(wrapper);
        return options.stream()
                .map(mealOptionConvert::toVo)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户当前选择状态*/
    @Resource
    private MealReservationConvert mealReservationConvert;

    @Override
    public MealReservationVO getUserCurentSelection(String userId, String activityId, Integer mealType, Date mealDate){
        LambdaQueryWrapper<MealReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MealReservation::getActivityId, activityId)
                .eq(MealReservation::getFairUserId, userId)
                .eq(MealReservation::getMealType, mealType)
                .apply(Objects.nonNull(mealDate),
                        "DATE_FORMAT(meal_date, '%Y-%m-%d') = DATE_FORMAT({0}, '%Y-%m-%d')", mealDate);

        MealReservation reservation = getOne(wrapper);
        if (reservation == null) {
            return null; // 未选择
        }

        return mealReservationConvert.toVo(reservation);
    }

    /**
     * 查询某活动、餐别下未投票的用户id（假设有参会用户表，伪代码接口）
     */
    @Override
    public List<FairUser> getNotChoosedFairUserList(MealOption mealOption) {
        String activityId = mealOption.getActivityId();
        if (StringUtils.isBlank(activityId)) {
            return null;
        }
        Integer mealType = mealOption.getMealType();
        LocalDate mealDate = mealOption.getMealDate();
        // 1. 查出所有应选餐用户
        List<FairUser> allUserList = mealReservationMapper.selectAllUserIdsByActivity(activityId);
        if (CollUtil.isEmpty(allUserList)) {
            return null;
        }
        log.debug("######## allUserList : {}", JSON.toJSONString(allUserList));

        // 2. 查出已选餐用户，包含自行解决用餐的部分用户
        List<MealReservation> choosedList = mealReservationMapper.selectChoosedMealReservationList(activityId, mealType, mealDate);
        if (CollUtil.isEmpty(choosedList)) {
            return allUserList;
        }

        // 3. 得到未投票用户id
        List<String> fairUserIdList = choosedList.stream().map(MealReservation::getFairUserId).collect(Collectors.toList());
        log.debug("######## choosedList fairUserIdList : {}", JSON.toJSONString(fairUserIdList));

        List<FairUser> notChoosedList = allUserList.stream()
                .filter(user -> !fairUserIdList.contains(user.getFairUserId()))
                .collect(Collectors.toList());
        log.debug("######## notChoosedList fairUserIdList : {}", JSON.toJSONString(notChoosedList));
        return notChoosedList;
    }

    /**
     * 批量插入自动“自行解决”记录
     */
    @Override
    public void batchInsertTimeoutSelfHelp(MealOption mealOption, List<FairUser> userList) {
        for (FairUser fairUser : userList) {
            MealReservation mr = new MealReservation();
            Integer mealType = mealOption.getMealType();
            if (mealType == DictOptionConstans.MEAL_TYPE_LUNCHBOX ) {
                String mealOptionId = mealOption.getMealOptionId();
                MealBox mealBox = mealBoxMapper.selectOne(
                        Wrappers.<MealBox>lambdaQuery()
                                .eq(!StringUtils.isBlank(mealOptionId), MealBox::getMealOptionId, mealOptionId)
                                .eq(MealBox::getRanking, 1));
                if (mealBox == null) {
                    continue;
                }
                mr.setMealBoxId(mealBox.getMealBoxId());
                mr.setMealOptionId(mealOptionId); // 餐饮套餐盒饭
            }

            if (mealType == DictOptionConstans.MEAL_TYPE_COMMUNAL_DINING ) {
                mr.setIsSelfHelp(1); // 1=自行解决
                mr.setMealOptionId(null);
                mr.setMealBoxId(null);
            }
            mr.setMealType(mealType); // 餐别

            mr.setMealReservationId(IdUtils.randomUUID()); // 设置随机主键
            mr.setActivityId(fairUser.getActivityId()); // 活动ID
            mr.setActivityCompanyId(fairUser.getActivityCompanyId()); // 活动公司ID
            mr.setFairUserId(fairUser.getFairUserId()); // 用户ID

            mr.setMealDate(mealOption.getMealDate());
            mr.setStatus(DictOptionConstans.COMMON_STATUS_CONFIRM); // 1-确认
            mr.setCreateDt(DateUtils.getNowDate()); // 创建时间
            mr.setRemarks("超时未选择，系统自动设为自行解决"); // 备注
            mealReservationMapper.insert(mr); // 插入数据库
        }
    }

    @Override
    public List<MealReservationVO> selectMealReservationVOList(MealReservationQueryParams mealReservationQueryParams) {
        return mealReservationMapper.selectMealReservationVOList(mealReservationQueryParams);
    }

    @Override
    public List<MealReservationDTO> selectTimeoutNotChoosedMealReservationList(MealReservationDTO mealReservationDTO) {
        return mealReservationMapper.selectTimeoutNotChoosedMealReservationList(mealReservationDTO);
    }

    /**
     * 批量插入自动“自行解决”记录
     */
    @Override
    public void batchInsertTimeoutSelfHelp(List<MealReservationDTO> dtoList) {
        List<MealReservation> mealReservationList = new ArrayList<MealReservation>();
        for (MealReservationDTO dto : dtoList) {
            Integer mealType = dto.getMealType();

            MealReservation mr = new MealReservation();
            mr.setMealReservationId(IdUtils.randomUUID()); // 设置随机主键
            mr.setMealType(mealType); // 餐别
            mr.setMealOptionId(dto.getMealOptionId());

            if (mealType == DictOptionConstans.MEAL_TYPE_LUNCHBOX ) {
                mr.setMealBoxId(dto.getMealBoxId());
                //mr.setIsSelfHelp(1); // 1=自行解决
            }

            if (mealType == DictOptionConstans.MEAL_TYPE_COMMUNAL_DINING ) {
                continue;
                //mr.setIsSelfHelp(1); // 1=自行解决
                //mr.setMealBoxId(null);
            }

            mr.setActivityId(dto.getActivityId()); // 活动ID
            mr.setActivityCompanyId(dto.getActivityCompanyId()); // 活动公司ID
            mr.setFairUserId(dto.getFairUserId()); // 用户ID

            mr.setMealDate(dto.getMealDate());
            mr.setStatus(DictOptionConstans.COMMON_STATUS_CONFIRM); // 1-确认
            mr.setCreateDt(DateUtils.getNowDate()); // 创建时间
            mr.setRemarks("超时未选择，系统自动设为自行解决"); // 备注
            log.debug("package MealReservationDTO to MealReservation: {}", JSON.toJSONString(mr));
            mealReservationList.add(mr);
        }

        if (CollUtil.isNotEmpty(mealReservationList)) {
            mealReservationMapper.insertTimeoutNotChoosedMealReservationList(mealReservationList);
        }
    }

}
