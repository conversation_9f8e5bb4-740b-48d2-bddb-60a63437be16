package com.szhr.ei.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.ActivityInvitationParams;
import com.szhr.ei.converter.request.AiOutCallParams;
import com.szhr.ei.converter.response.ActivityInvitationCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationOptionalCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationVO;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.mapper.ActivityInvitationMapper;
import com.szhr.ei.service.IActivityInvitationService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邀约库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class ActivityInvitationServiceImpl extends ServiceImpl<ActivityInvitationMapper, ActivityInvitation> implements IActivityInvitationService {
    @Resource
    private ActivityInvitationMapper activityInvitationMapper;

//@Resource
//    private WebClient webClient;

    @Override
    public List<ActivityInvitationVO> selectActivityInvitationList(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.selectActivityInvitationList(activityInvitation);
    }

    @Override
    public List<ActivityInvitationCompanyVO> listCompany(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.listCompany(activityInvitation);
    }

    @Override
    public List<ActivityInvitationOptionalCompanyVO> listOptionalCompany(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.listOptionalCompany(activityInvitation);
    }



      @Override
      public int aiOutCall(AiOutCallParams aiOutCallParams, Long userId) {
        int result = 0;
        // 1. 调用外部HTTP接口
        JSONObject requestBody = new JSONObject();
        requestBody.put("ids", aiOutCallParams.getIds());
        requestBody.put("phoneNumbers", aiOutCallParams.getPhoneNumbers());
        String uri = "https://external-api.example.com/call";

//        webClient.post()
//          .uri(uri)
//          .contentType(MediaType.APPLICATION_JSON)
//          .bodyValue(requestBody.toString())
//          .retrieve()
//          .toBodilessEntity()
//          .map(response -> {
//            // 2. 检查响应是否成功
//            if (response.getStatusCode().is2xxSuccessful()) {
//              for(String id : aiOutCallParams.getIds()){
//                // 2.1 更新邀约记录状态
//                ActivityInvitation invitation = new ActivityInvitation();
//                invitation.setId(id);
////                invitation.setStatus("OUT_CALLED");
////                invitation.setUpdateBy(userId.toString());
//                activityInvitationMapper.updateById(invitation);
//              }
//            }
//            return 0;
//          })
//          .block();

        return result;
      }

    public JSONObject addBatchAiCall(JSONObject requestDate){
        JSONObject result = new JSONObject();

        try{
            //解析参数
            String activityId = requestDate.getString("activityId");
            JSONArray phoneNumbers = requestDate.getJSONArray("phones");


        }

        return result;
    }

    public String getAiCallPrefix(){
        return "http://ai23.ytcall.net/api.php";
    }

    public  String getAiCallToken(){
        return "224a305a71c111f0b97700163e0062e1";
    }

    public String getAiCallCompangId(){
        return "637";
    }
}
