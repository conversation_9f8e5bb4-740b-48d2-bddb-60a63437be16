package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.ActivityInvitationParams;
import com.szhr.ei.converter.response.ActivityInvitationCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationOptionalCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationVO;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.mapper.ActivityInvitationMapper;
import com.szhr.ei.service.IActivityInvitationService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邀约库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class ActivityInvitationServiceImpl extends ServiceImpl<ActivityInvitationMapper, ActivityInvitation> implements IActivityInvitationService {
    @Resource
    private ActivityInvitationMapper activityInvitationMapper;

    @Override
    public List<ActivityInvitationVO> selectActivityInvitationList(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.selectActivityInvitationList(activityInvitation);
    }

    @Override
    public List<ActivityInvitationCompanyVO> listCompany(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.listCompany(activityInvitation);
    }

    @Override
    public List<ActivityInvitationOptionalCompanyVO> listOptionalCompany(ActivityInvitationParams activityInvitation) {
        return activityInvitationMapper.listOptionalCompany(activityInvitation);
    }
}
