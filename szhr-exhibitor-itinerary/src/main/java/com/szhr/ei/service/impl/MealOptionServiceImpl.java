package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.mapstruct.MealOptionConvert;
import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.mapper.ActivityMapper;
import com.szhr.ei.mapper.MealOptionMapper;
import com.szhr.ei.service.IMealOptionService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 餐饮套餐信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MealOptionServiceImpl extends ServiceImpl<MealOptionMapper, MealOption> implements IMealOptionService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MealOptionMapper mealOptionMapper;

    @Resource
    private MealOptionConvert mealOptionConvert;

    @Resource
    private ActivityMapper activityMapper;

    @Override
    public List<MealOption> listByCondition(MealOption mealOption) {
        return mealOptionMapper.selectList(
                Wrappers.<MealOption>lambdaQuery()
                        .eq(!StringUtils.isBlank(mealOption.getMealOptionId()), MealOption::getMealOptionId, mealOption.getMealOptionId())
                        .eq(StringUtils.isNotBlank(mealOption.getActivityId()), MealOption::getActivityId, mealOption.getActivityId())
                        .eq(Objects.nonNull(mealOption.getMealType()), MealOption::getMealType, mealOption.getMealType())
                        .eq(Objects.nonNull(mealOption.getMealDate()), MealOption::getMealDate, mealOption.getMealDate())
                        .lt(Objects.nonNull(mealOption.getSelectionEndTime()), MealOption::getSelectionEndTime, mealOption.getSelectionEndTime())
                        .like(StringUtils.isNotBlank(mealOption.getRestaurantName()), MealOption::getRestaurantName, mealOption.getRestaurantName())
                        .eq(Objects.nonNull(mealOption.getStatus()), MealOption::getStatus, mealOption.getStatus()));

    }

    @Override
    public List<MealOptionVO> selectVOList(MealOptionQueryParams mealOptionQueryParams) {
        return mealOptionMapper.selectVOList(mealOptionQueryParams);
    }

    @Override
    public boolean setImgUrlToNull(String id) {
        LambdaUpdateWrapper<MealOption> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MealOption::getMealOptionId, id).set(MealOption::getPhotoUrl, null);  // 设置为 NULL
        int updateRow = mealOptionMapper.update(null, wrapper);
        return updateRow > 0;
    }

    @Override
    public List<MealOption> selectTimeoutNotChoosedMealOptionList() {
        return mealOptionMapper.selectTimeoutNotChoosedMealOptionList();
    }

}
