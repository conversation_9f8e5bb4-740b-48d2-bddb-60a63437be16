package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.MealBoxQueryParams;
import com.szhr.ei.converter.response.MealBoxVO;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.domain.MealOption;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.MealBoxMapper;
import com.szhr.ei.domain.MealBox;
import com.szhr.ei.service.IMealBoxService;

import java.util.List;
import java.util.Objects;

/**
 * 午饭盒饭选项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class MealBoxServiceImpl extends ServiceImpl<MealBoxMapper, MealBox> implements IMealBoxService {
    @Resource
    private MealBoxMapper mealBoxMapper;

    @Override
    public List<MealBoxVO> selectVOList(MealBoxQueryParams mealBoxQueryParams) {
        return mealBoxMapper.selectVOList(mealBoxQueryParams);
    }

    @Override
    public List<MealBox> listByCondition(MealBox mealBox) {
        return mealBoxMapper.selectList(
            Wrappers.<MealBox>lambdaQuery()
                    .eq(!StringUtils.isBlank(mealBox.getMealBoxId()), MealBox::getMealBoxId, mealBox.getMealBoxId())
                    .eq(StringUtils.isNotBlank(mealBox.getMealOptionId()), MealBox::getMealOptionId, mealBox.getMealOptionId())
                    .like(StringUtils.isNotBlank(mealBox.getBoxName()), MealBox::getBoxName, mealBox.getBoxName()));

    }

    @Override
    public boolean setImgUrlToNull(String id) {
        LambdaUpdateWrapper<MealBox> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MealBox::getMealOptionId, id).set(MealBox::getPhotoUrl, null);  // 设置为 NULL
        int updateRow = mealBoxMapper.update(null, wrapper);
        if (updateRow > 0) {
            return true;
        }
        return false;
    }
}
