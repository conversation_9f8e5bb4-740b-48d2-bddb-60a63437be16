package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.request.ActivityCompanyParams;
import com.szhr.ei.converter.response.ActivityCompanyVO;
import com.szhr.ei.domain.ActivityCompany;

import java.util.List;

/**
 * 报名单位信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IActivityCompanyService extends IService<ActivityCompany> {
    List<ActivityCompanyVO> selectActivityList(ActivityCompanyParams activityCompany);
}
