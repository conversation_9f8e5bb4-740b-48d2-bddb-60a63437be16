package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.response.HotelStatVO;
import com.szhr.ei.domain.Hotel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.ei.converter.response.HotelVO;

/**
 * 酒店信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IHotelService extends IService<Hotel> {

    List<HotelVO> listByCondition(Hotel hotel);

    boolean setImgUrlToNull(String id);

    List<HotelStatVO> queryHotelStatVOList(Hotel hotel);
}
