package com.szhr.ei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szhr.common.core.domain.AjaxResult;
import com.szhr.ei.domain.ItineraryTicket;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 出行航班或火车班次记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IItineraryTicketService extends IService<ItineraryTicket> {

    List<ItineraryTicketVO> selectItineraryTicketVOList(ItineraryTicketParams itineraryTicketParams);

    AjaxResult saveBatchImport(String activityId, MultipartFile file) throws IOException;
}
