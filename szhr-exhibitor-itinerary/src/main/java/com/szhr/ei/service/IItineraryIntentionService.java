package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.request.ItineraryIntentionQueryParams;
import com.szhr.ei.converter.response.ItineraryIntentionVO;
import com.szhr.ei.domain.ItineraryIntention;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 预订行程飞机高铁意向Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IItineraryIntentionService extends IService<ItineraryIntention> {
    List<ItineraryIntentionVO> selectItineraryIntentionVOList(ItineraryIntentionQueryParams itineraryIntentionQueryParams);
}
