package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.domain.RoomType;
import com.szhr.ei.mapper.RoomTypeMapper;
import com.szhr.ei.service.IRoomTypeService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 房型信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class RoomTypeServiceImpl extends ServiceImpl<RoomTypeMapper, RoomType> implements IRoomTypeService {
    @Resource
    private RoomTypeMapper roomTypeMapper;

    @Override
    public List<RoomType> listByCondition(RoomType roomType) {
        LambdaQueryWrapper<RoomType> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isBlank(roomType.getId())) {
            wrapper.eq(RoomType::getId, roomType.getId());
        }
        if (StringUtils.isNotBlank(roomType.getName())) {
            wrapper.like(RoomType::getName, roomType.getName());
        }

        return roomTypeMapper.selectList(wrapper);

    }
}
