package com.szhr.ei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.szhr.ei.converter.request.ItineraryIntentionQueryParams;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryIntentionVO;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.szhr.ei.mapper.ItineraryIntentionMapper;
import com.szhr.ei.domain.ItineraryIntention;
import com.szhr.ei.service.IItineraryIntentionService;

import java.util.List;

/**
 * 预订行程飞机高铁意向Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class ItineraryIntentionServiceImpl extends ServiceImpl<ItineraryIntentionMapper, ItineraryIntention> implements IItineraryIntentionService {
    @Resource
    private ItineraryIntentionMapper itineraryIntentionMapper;

    @Override
    public List<ItineraryIntentionVO> selectItineraryIntentionVOList(ItineraryIntentionQueryParams itineraryIntentionQueryParams) {
        return itineraryIntentionMapper.selectItineraryIntentionVOList(itineraryIntentionQueryParams);
    }
}
