package com.szhr.ei.service;

import java.util.List;

import com.szhr.ei.converter.request.MealBoxQueryParams;
import com.szhr.ei.converter.response.HotelVO;
import com.szhr.ei.converter.response.MealBoxVO;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.domain.MealBox;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 午饭盒饭选项Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IMealBoxService extends IService<MealBox> {

    List<MealBoxVO> selectVOList(MealBoxQueryParams mealBoxQueryParams);

    List<MealBox> listByCondition(MealBox mealBox);

    boolean setImgUrlToNull(String id);
}
