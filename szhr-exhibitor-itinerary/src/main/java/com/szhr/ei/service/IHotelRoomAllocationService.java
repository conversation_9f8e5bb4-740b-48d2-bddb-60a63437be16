package com.szhr.ei.service;

import java.util.List;

import com.szhr.common.core.domain.AjaxResult;
import com.szhr.ei.converter.request.HotelRoomAllocationQueryParams;
import com.szhr.ei.converter.response.*;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 房间分配记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IHotelRoomAllocationService extends IService<HotelRoomAllocation> {
    List<HotelRoomAllocationExport> selectHotelRoomAllocationExportList(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams);

    List<HotelRoomAllocation> listByCondition(HotelRoomAllocation hotelRoomAllocation);
    List<HotelRoomAllocationVO> listByCondition(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams);

    String sendSmsMail(List<String> ids);

    List<HotelRoomAllocationResponce> selectHotelInformationList(HotelRoomAllocation hotelRoomAllocation);
}
