package com.szhr.ei.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.uuid.IdUtils;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.HotelDailyStat;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IHotelDailyStatService;
import com.szhr.ei.service.IHotelRoomAllocationService;
import com.szhr.ei.service.IHotelService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订餐投票超时自动处理任务
 */
@Component
public class HotelDailyStatTask {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IActivityService activityService;

    @Resource
    private IHotelRoomAllocationService hotelRoomAllocationService;

    @Resource
    private IHotelDailyStatService hotelDailyStatService;

    public void handleHotelDailyStat() {
        // 查询所有正在举办的活动
        Date nowDate = DateUtils.getNowDate();
        System.out.println("######## handleTimeoutReservations nowDate: " + nowDate);
        List<Activity> effectiveActivitys = activityService.list(
                Wrappers.<Activity>lambdaQuery()
                        .isNull(Activity::getDeleteDt)
                        .apply("DATE_FORMAT({0}, '%%Y-%%m-%%d') between DATE_FORMAT(start_date, '%%Y-%%m-%%d') and DATE_FORMAT(end_date, '%%Y-%%m-%%d')", nowDate));
        System.out.println("######## effectiveActivitys size: " + effectiveActivitys.size());

        // 查询所有正在举办的活动
        for (Activity item : effectiveActivitys) {
            String activityId = item.getId();
            // 查询所有酒店
            List<HotelRoomAllocation> hasAllocations = hotelRoomAllocationService.list(
                    Wrappers.<HotelRoomAllocation>lambdaQuery()
                            .eq(HotelRoomAllocation::getStatus, 1)
                            .eq(HotelRoomAllocation::getActivityId, activityId));
            if (CollUtil.isNotEmpty(hasAllocations)) {
                Map<String, List<HotelRoomAllocation>> hotelIdsMap = hasAllocations.stream()
                        .collect(Collectors.groupingBy(HotelRoomAllocation::getHotelId));
                // 打印结果
                hotelIdsMap.forEach((hotelId, allocationsList) -> {
                    log.debug("######## Hotel hotelId:{} ", hotelId);
                    Map<Date, List<HotelRoomAllocation>> hotelReserveDatesMap = allocationsList.stream()
                            .collect(Collectors.groupingBy(HotelRoomAllocation::getCheckInDate));
                    hotelReserveDatesMap.forEach((reserveDate, reserveDatesList) -> {
                        log.debug("######## Hotel reserveDate: " + reserveDate);
                        int personCnt = reserveDatesList.size();
                        HotelDailyStat hotelDailyStat = hotelDailyStatService.getOne(Wrappers.<HotelDailyStat>lambdaQuery()
                                .eq(HotelDailyStat::getActivityId, activityId)
                                .eq(HotelDailyStat::getHotelId, hotelId)
                                .apply("DATE_FORMAT(reserve_date, '%%Y-%%m-%%d') = DATE_FORMAT({0}, '%%Y-%%m-%%d')", reserveDate));
                        log.debug("######## 已预约记录: {}" + JSON.toJSONString(hotelDailyStat));
                        if (hotelDailyStat != null) {
                            Date updateDate = DateUtils.getNowDate();
                            hotelDailyStat.setCurrNum(personCnt);
                            hotelDailyStat.setUpdateDt(updateDate);
                            hotelDailyStatService.updateById(hotelDailyStat);
                        } else {
                            hotelDailyStat = new HotelDailyStat();
                            hotelDailyStat.setId(IdUtils.randomUUID());
                            hotelDailyStat.setActivityId(activityId);
                            hotelDailyStat.setHotelId(hotelId);
                            hotelDailyStat.setCurrNum(personCnt);
                            hotelDailyStat.setReserveDate(reserveDate);
                            Date createDate = DateUtils.getNowDate();
                            hotelDailyStat.setCreateDt(createDate);
                            hotelDailyStat.setUpdateDt(createDate);
                            hotelDailyStatService.save(hotelDailyStat);
                        }
                    });

                });
            }
        }
    }
}

