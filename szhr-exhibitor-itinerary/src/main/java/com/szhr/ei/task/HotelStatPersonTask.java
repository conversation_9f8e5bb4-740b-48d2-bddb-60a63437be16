package com.szhr.ei.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.szhr.common.utils.DateUtils;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.domain.Hotel;
import com.szhr.ei.domain.HotelRoomAllocation;
import com.szhr.ei.service.IActivityService;
import com.szhr.ei.service.IHotelDailyStatService;
import com.szhr.ei.service.IHotelRoomAllocationService;
import com.szhr.ei.service.IHotelService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 酒店入住统计人数自动处理任务
 */
@Component("hotelStatPersonTask")
public class HotelStatPersonTask {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IActivityService activityService;

    @Resource
    private IHotelService hotelService;

    @Resource
    private IHotelRoomAllocationService hotelRoomAllocationService;

    public void handleHotelStatPerson() {
        // 查询所有正在举办的活动
        Date nowDate = DateUtils.getNowDate();
        List<Activity> effectiveActivitys = activityService.list(
                Wrappers.<Activity>lambdaQuery()
                        .isNull(Activity::getDeleteDt)
                        .apply("DATE_FORMAT({0}, '%%Y-%%m-%%d') between DATE_FORMAT(start_date, '%%Y-%%m-%%d') and DATE_FORMAT(end_date, '%%Y-%%m-%%d')", nowDate));
        if (CollUtil.isEmpty(effectiveActivitys)) {
           return;
        }
        log.debug("######## effectiveActivitys size:{} ", effectiveActivitys.size());

        // 查询所有正在举办的活动
        for (Activity item : effectiveActivitys) {
            String activityId = item.getId();
            // 查询所有酒店
            List<HotelRoomAllocation> hasAllocations = hotelRoomAllocationService.list(
                    Wrappers.<HotelRoomAllocation>lambdaQuery()
                            .eq(HotelRoomAllocation::getStatus, 1)
                            .eq(HotelRoomAllocation::getActivityId, activityId));
            if (CollUtil.isNotEmpty(hasAllocations)) {
                Map<String, List<HotelRoomAllocation>> hotelIdsMap = hasAllocations.stream()
                        .collect(Collectors.groupingBy(HotelRoomAllocation::getHotelId));

                hotelIdsMap.forEach((hotelId, allocationsList) -> {
                    log.debug("######## Hotel hotelId:{} ", hotelId);
                    Hotel hotel = new Hotel();
                    hotel.setHotelId(hotelId);
                    hotel.setCurrNum(allocationsList.size());
                    hotelService.updateById(hotel);

                });
            }
        }
    }
}

