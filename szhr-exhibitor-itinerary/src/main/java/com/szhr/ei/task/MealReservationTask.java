package com.szhr.ei.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.szhr.common.utils.DateUtils;
import com.szhr.common.utils.StringUtils;
import com.szhr.ei.converter.dto.MealReservationDTO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.service.IMealOptionService;
import com.szhr.ei.service.IMealReservationService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 订餐投票超时自动处理任务
 */
@Component("mealReservationTask")
public class MealReservationTask {
    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMealOptionService mealOptionService; // 注入套餐服务
    @Resource
    private IMealReservationService mealReservationService; // 注入订餐服务

    // 每5分钟执行一次的定时任务
    public void handleTimeoutNotChoosedMealReservation() {
        Date nowDate = DateUtils.getNowDate();
        log.debug("######## mealReservationTask start: {} ", nowDate);
        List<MealReservationDTO> dtoList = mealReservationService.selectTimeoutNotChoosedMealReservationList(null);
        log.debug("######## dtoList: {} ", JSON.toJSONString(dtoList));
        if (CollUtil.isNotEmpty(dtoList)) {
            mealReservationService.batchInsertTimeoutSelfHelp(dtoList);
        }

    }

    // 每5分钟执行一次的定时任务
    public void handleTimeoutReservations() {
        // 查询所有套餐（meal_option）
        List<MealOption> allOptions = mealOptionService.list();
        Date nowDate = DateUtils.getNowDate();
        for (MealOption mealOption : allOptions) {
            log.debug("######## mealOption: {} ", JSON.toJSONString(mealOption));
            if (mealOption.getStatus() == null || mealOption.getStatus() == 0) {
                continue;
            }
            if (StringUtils.isBlank(mealOption.getActivityId())) {
                continue;
            }
            if (mealOption.getMealType() == null || !(mealOption.getMealType() == 1 || mealOption.getMealType() == 2)) {
                continue;
            }
            // 判断当前时间是否超过选餐截止时间
            if (mealOption.getSelectionEndTime() != null && nowDate.before(mealOption.getSelectionEndTime())) {
                // 查询未投票用户ID
                List<FairUser> notChoosedFairUserList = mealReservationService.getNotChoosedFairUserList(mealOption);
                log.debug("######## notChoosedFairUserList: {} ", JSON.toJSONString(notChoosedFairUserList));
                if (CollUtil.isNotEmpty(notChoosedFairUserList)) {
                    // 批量自动补录“自行解决”记录
                    mealReservationService.batchInsertTimeoutSelfHelp(mealOption, notChoosedFairUserList);
                }
            }
        }
    }
}