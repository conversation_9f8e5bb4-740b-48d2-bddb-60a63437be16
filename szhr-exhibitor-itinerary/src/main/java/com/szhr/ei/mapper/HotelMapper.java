package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.HotelStatVO;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.domain.Hotel;

/**
 * 酒店信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface HotelMapper extends BaseMapper<Hotel> {

    List<HotelStatVO> queryHotelStatVOList(Hotel hotel);

}
