package com.szhr.ei.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ActivityCompanyParams;
import com.szhr.ei.converter.response.ActivityCompanyVO;
import com.szhr.ei.domain.ActivityCompany;

import java.util.List;

/**
 * 报名单位信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ActivityCompanyMapper extends BaseMapper<ActivityCompany> {

    List<ActivityCompanyVO> selectActivityList(ActivityCompanyParams activityCompany);

}
