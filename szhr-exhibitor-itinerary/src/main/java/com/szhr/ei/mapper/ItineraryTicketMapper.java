package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.domain.ItineraryTicket;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;

/**
 * 出行航班或火车班次记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ItineraryTicketMapper extends BaseMapper<ItineraryTicket> {

    List<ItineraryTicketVO> selectItineraryTicketVOList(ItineraryTicketParams itineraryTicketParams);
}
