package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.FairUserParams;
import com.szhr.ei.converter.response.FairUserVO;
import com.szhr.ei.domain.FairUser;

/**
 * 用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface FairUserMapper extends BaseMapper<FairUser> {
    List<FairUserVO> selectFairUserList(FairUserParams fairUserParams);
}
