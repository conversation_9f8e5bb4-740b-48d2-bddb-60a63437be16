package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ActivityInvitationParams;
import com.szhr.ei.converter.response.ActivityInvitationAICallParams;
import com.szhr.ei.converter.response.ActivityInvitationCompanyVO;
import com.szhr.ei.converter.response.ActivityInvitationOptionalCompanyVO;
import com.szhr.ei.domain.ActivityInvitation;
import com.szhr.ei.converter.response.ActivityInvitationVO;

/**
 * 邀约库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ActivityInvitationMapper extends BaseMapper<ActivityInvitation> {
    List<ActivityInvitationVO> selectActivityInvitationList(ActivityInvitationParams activityInvitation);

    List<ActivityInvitationCompanyVO> listCompany(ActivityInvitationParams activityInvitation);

    List<ActivityInvitationOptionalCompanyVO> listOptionalCompany(ActivityInvitationParams activityInvitation);

    int updateByActivityIdAndPhones(ActivityInvitationAICallParams activityInvitationAICallParams);
}
