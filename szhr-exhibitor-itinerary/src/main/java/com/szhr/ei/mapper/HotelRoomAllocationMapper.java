package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.HotelRoomAllocationQueryParams;
import com.szhr.ei.converter.response.HotelRoomAllocationExport;
import com.szhr.ei.converter.response.HotelRoomAllocationResponce;
import com.szhr.ei.converter.response.HotelRoomSmsMail;
import com.szhr.ei.domain.HotelRoomAllocation;


/**
 * 房间分配记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface HotelRoomAllocationMapper extends BaseMapper<HotelRoomAllocation> {

    List<HotelRoomAllocationExport> selectHotelRoomAllocationExportList(HotelRoomAllocationQueryParams hotelRoomAllocationQueryParams);

    HotelRoomSmsMail getHotelRoomSmsMailById(String id);

    List<HotelRoomAllocationResponce> selectHotelInformationList(HotelRoomAllocation hotelRoomAllocation);
}
