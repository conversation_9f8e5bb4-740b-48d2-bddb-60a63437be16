package com.szhr.ei.mapper;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.dto.MealReservationDTO;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.request.MealReservationQueryParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.converter.response.MealReservationVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.MealOption;
import com.szhr.ei.domain.MealReservation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.domain.MealReservation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
/**
 * 用户订餐记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface MealReservationMapper extends BaseMapper<MealReservation> {

    List<MealReservationVO> selectMealReservationVOList(MealReservationQueryParams mealReservationQueryParams);

    /**
     * 统计选择某个套餐的用户数（只统计非自行解决的数量）
     */
    @Select("SELECT COUNT(*) FROM meal_reservation " +
            "WHERE meal_option_id = #{mealOptionId} AND is_self_help = 0")
    int countSelectedUsers(@Param("mealOptionId") String mealOptionId);

    /**
     * 查询某活动所有用户ID
     */
    @Select("SELECT * FROM fair_user WHERE activity_id = #{activityId} and delete_dt is null")//fair_user
    List<FairUser> selectAllUserIdsByActivity(@Param("activityId") String activityId);
    //*返回所有字段，fair_user
    //要返回company，把mealreservation冗余字段

    /**
     * 查询某活动、餐别已投票的用户ID
     */
    @Select("SELECT * FROM meal_reservation WHERE activity_id = #{activityId} AND meal_type = #{mealType} AND DATE_FORMAT(meal_date, '%Y-%m-%d') = DATE_FORMAT(#{mealDate}, '%Y-%m-%d')")
    List<MealReservation> selectChoosedMealReservationList(@Param("activityId") String activityId, @Param("mealType") Integer mealType, @Param("mealDate") LocalDate mealDate);

    /**
     * 查询某活动、餐别已投票的用户ID
     */
    @Select("SELECT * FROM meal_reservation WHERE meal_option_id = #{mealOptionId} and status = 1")
    List<MealReservation> selectChoosedMealReservationList(@Param("mealOptionId") String mealOptionId);

    List<MealReservationDTO> selectTimeoutNotChoosedMealReservationList(MealReservationDTO mealReservationDTO);

    void insertTimeoutNotChoosedMealReservationList(List<MealReservation> mealReservationList);

}

