package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.response.PickupScheduleVO;
import com.szhr.ei.domain.PickupSchedule;
import org.apache.ibatis.annotations.Param;

/**
 * 接送安排Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface PickupScheduleMapper extends BaseMapper<PickupSchedule> {


    // 查询指定日期的接送安排（返回PickupScheduleVO）
    List<PickupScheduleVO> selectPickupScheduleVOsByDate(@Param("activityId") String activityId,
                                                         @Param("scheduleDate") String scheduleDate);
}
