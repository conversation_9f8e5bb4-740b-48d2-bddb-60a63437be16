package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ItineraryIntentionQueryParams;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryIntentionVO;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.domain.ItineraryIntention;

/**
 * 预订行程飞机高铁意向Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface ItineraryIntentionMapper extends BaseMapper<ItineraryIntention> {

    List<ItineraryIntentionVO> selectItineraryIntentionVOList(ItineraryIntentionQueryParams itineraryIntentionQueryParams);

}
