package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.MealBoxQueryParams;
import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealBoxVO;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealBox;

/**
 * 午饭盒饭选项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface MealBoxMapper extends BaseMapper<MealBox> {
    List<MealBoxVO> selectVOList(MealBoxQueryParams mealBoxQueryParams);

}
