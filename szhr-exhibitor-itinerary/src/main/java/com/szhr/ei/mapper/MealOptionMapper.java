package com.szhr.ei.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ActivityParams;
import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.ActivityVO;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;

/**
 * 餐饮套餐信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface MealOptionMapper extends BaseMapper<MealOption> {

    List<MealOptionVO> selectVOList(MealOptionQueryParams mealOptionQueryParams);

    List<MealOption> selectTimeoutNotChoosedMealOptionList();

}
