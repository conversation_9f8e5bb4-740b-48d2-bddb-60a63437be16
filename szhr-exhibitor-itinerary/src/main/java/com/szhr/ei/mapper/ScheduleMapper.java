package com.szhr.ei.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ScheduleQueryParams;
import com.szhr.ei.converter.response.ScheduleVO;
import com.szhr.ei.domain.Schedule;
import org.apache.ibatis.annotations.Param;

/**
 * 日程安排信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ScheduleMapper extends BaseMapper<Schedule> {

    List<Schedule> selectScheduleList(ScheduleQueryParams schedule);

}
