package com.szhr.ei.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.szhr.ei.converter.request.ActivityParams;
import com.szhr.ei.converter.request.ActivityQueryParams;
import com.szhr.ei.converter.response.ActivityQueryResponseFromUser;
import com.szhr.ei.domain.Activity;
import com.szhr.ei.converter.response.ActivityVO;

import java.util.List;

/**
 * 招展行程活动Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ActivityMapper extends BaseMapper<Activity> {

    List<ActivityVO> selectActivityList(ActivityParams activity);

    List<ActivityQueryResponseFromUser> seclectActivityBasedOnFairUser(ActivityQueryParams activityQueryParams);
}
