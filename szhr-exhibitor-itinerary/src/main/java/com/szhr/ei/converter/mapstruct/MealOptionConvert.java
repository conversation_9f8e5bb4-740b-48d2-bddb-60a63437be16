package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.request.MealOptionQueryParams;
import com.szhr.ei.converter.response.MealOptionVO;
import com.szhr.ei.domain.MealOption;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface MealOptionConvert {
    MealOptionConvert INSTANCE = Mappers.getMapper(MealOptionConvert.class);
    MealOptionVO toVo(MealOption mealOption);
    MealOptionQueryParams toParams(MealOption mealOption);

}
