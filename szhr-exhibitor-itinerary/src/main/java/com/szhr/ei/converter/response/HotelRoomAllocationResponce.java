package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 入住酒店信息查询
 *
 * <AUTHOR>
 * @date 2025-06-13
 */

@Data
public class HotelRoomAllocationResponce implements Serializable {

    private String hotelName;

    private String hotelAddress;

    private String frontDeskPhone;

    private String imgUrl;

    private String roomType;

    private String fairUserName;


    /** 入住日期 */
    private Date checkInDate;

    /** 离开日期 */
    private Date checkOutDate;



}
