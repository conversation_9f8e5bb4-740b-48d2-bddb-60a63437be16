package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import com.szhr.common.utils.poi.DataDictHandler;
import com.szhr.ei.domain.ActivityInvitation;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityInvitationVO extends ActivityInvitation {

  /**
   * 活动ID
   */
  @NotBlank(message = "活动ID不能为空")
  @TableField(value = "activity_id")
  private String activityId;

  /**
   * 公司名称
   */
  @NotBlank(message = "公司名称不能为空")
  @Excel(name = "公司名称", sort = 1, width = 30)
  @TableField(value = "company_name")
  private String companyName;

  /**
   * 所在行业（多选）
   */
//  @Excel(name = "所在行业", handler = DataDictHandler.class, args = "INDUSTRY")
  @TableField(value = "industry")
  private String industry;

  @Excel(name = "单位类型", sort = 3, width = 40, handler = DataDictHandler.class, args = "COMPANY_TYPE")
  @TableField(value = "company_type")
  private String companyType;

  /**
   * 公司性质
   */
  @Excel(name = "公司性质", sort = 4, width = 40, handler = DataDictHandler.class, args = "COMPANY_PROP")
  @TableField(value = "company_prop")
  private String companyProp;

  /**
   * 公司规模
   */
  @Excel(name = "公司规模", sort = 5, handler = DataDictHandler.class, args = "COMPANY_SCALE")
  @TableField(value = "company_scale")
  private String companyScale;

  /**
   * 联系人
   */
  @Excel(name = "联系人", sort = 6)
  @TableField(value = "contact_person")
  private String contactPerson;

  /**
   * 移动电话
   */
  @NotBlank(message = "移动电话不能为空")
  @Excel(name = "移动电话", sort = 7)
  @TableField(value = "mobile_phone", typeHandler = AESTypeHandler.class)
  private String mobilePhone;

  /**
   * 电子邮箱
   */
  @Excel(name = "电子邮箱", sort = 8)
  @TableField(value = "email")
  private String email;

  @Excel(name = "创建人用户名", sort = 21)
  @TableField(value = "create_user_name")
  private String createUserName;

  @Excel(name = "管理员用户名", sort = 23)
  @TableField(value = "manage_user_name")
  private String manageUserName;

  @Excel(name = "所在行业", sort = 2, width = 30)
  @TableField(value = "industry_name")
  private String industryName;
}
