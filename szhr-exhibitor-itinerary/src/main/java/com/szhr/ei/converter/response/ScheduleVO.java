package com.szhr.ei.converter.response;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * 日程安排信息对象 schedule
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@TableName(value = "schedule", autoResultMap = true)
public class ScheduleVO implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String scheduleId;

    /** 关联活动ID */
    @Excel(name = "关联活动ID" )
    private String activityId;

    /** 出发地 */
    @Excel(name = "出发地" )
    private String departureAddress;

    /** 目的地 */
    @Excel(name = "目的地" )
    private String arrivalAddress;

    /** 日程日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "日程日期", width = 30, dateFormat = "yyyy-MM-dd" )
    private Date scheduleDate;

    /** 出发时间 */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "出发时间", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime scheduleTime;

    /** 司机姓名 */
    @Excel(name = "司机姓名" )
    private String driverName;

    /** 司机电话 */
    @Excel(name = "司机电话" )
    private String driverNumber;

    /** 工作人员姓名 */
    @Excel(name = "工作人员姓名" )
    private String staffName;

    /** 工作人员电话 */
    @Excel(name = "工作人员电话" )
    private String staffNumber;

    /*ber;* 车牌号 */
    @Excel(name = "车牌号" )
    private String carNumber;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd" )
    private Date createdAt;

    /** 活动名称 */
    @Excel(name = "活动名称" )
    private String activityName;

}
