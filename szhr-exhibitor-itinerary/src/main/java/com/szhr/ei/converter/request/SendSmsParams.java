package com.szhr.ei.converter.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 出行飞机或火车班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class SendSmsParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** 手机号码 */
    @NotBlank(message = "手机号码不能为空")
    private String mobilePhone;

    /** 图片验证码 */
    @NotBlank(message = "图片验证码不能为空")
    private String code;

    /** 图片验证码编号*/
    @NotBlank(message = "图片验证码编号不能为空")
    private String uuid;



}
