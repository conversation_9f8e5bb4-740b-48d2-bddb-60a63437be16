package com.szhr.ei.converter.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.constant.ReadConverterExpConstants;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 用户订餐记录对象 meal_reservation
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class MealReservationDTO implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    /** 关联活动ID */
    @Excel(name = "关联活动编号" )
    private String activityId;

    /** 用户ID */
    @Excel(name = "用户编号" )
    private String fairUserId;

    @Excel(name = "单位编号", width = 40)
    private String activityCompanyId;

    /** 餐饮选项ID */
    @NotBlank(message = "餐饮选项编号不能为空")
    @Excel(name = "餐饮选项编号" )
    private String mealOptionId;

    /** 午饭盒饭编号 */
    @Excel(name = "午饭盒饭编号" )
    private String mealBoxId;

    /** 用餐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd" )
    private LocalDate mealDate;

    /** 餐饮类型（1-午餐盒饭，2-聚餐） */
    @NotBlank(message = "餐饮类型不能为空")
    @Excel(name = "餐饮类型")
    private Integer mealType;





}
