package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.constant.ReadConverterExpConstants;
import com.szhr.ei.domain.FairUser;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class FairUserVO extends FairUser {

    /** 公司名称 */
    @NotBlank(message = "公司名称不能为空")
    @Excel(name = "公司名称", sort = 1, width = 30)
    @TableField(value = "company_name")
    private String companyName;

    @Excel(name = "单位类型", sort = 2, width = 40, readConverterExp = ReadConverterExpConstants.COMPANY_TYPE)
    @TableField(value = "company_type")
    private String companyType;

    @Excel(name = "创建人用户名", sort = 21 )
    @TableField(value = "create_user_name")
    private String createUserName;
}
