package com.szhr.ei.converter.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Data
public class FairUserLoginParams {

    /**
     * 活动编号
     */
    @NotBlank(message = "活动编号不能为空")
    private String activityId;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobilePhone;

    /**
     * 手机短信验证码
     */
    @NotBlank(message = "手机短信验证码不能为空")
    private String smsCode;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;



}
