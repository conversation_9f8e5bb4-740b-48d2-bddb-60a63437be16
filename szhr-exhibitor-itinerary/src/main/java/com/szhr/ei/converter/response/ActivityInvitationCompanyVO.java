package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import lombok.Data;

@Data
public class ActivityInvitationCompanyVO {

    /** 活动ID */
    @TableField(value = "activity_id")
    private String activityId;

    /** 活动关联单位ID */
    @TableField(value = "activity_company_id")
    private String activityCompanyId;

    /** 公司名称 */
    @Excel(name = "公司名称" )
    @TableField(value = "company_name")
    private String companyName;
}
