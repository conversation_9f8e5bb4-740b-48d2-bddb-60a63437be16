package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 房间分配记录对象 hotel_room_allocation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class HotelRoomAllocationExport implements Serializable {
  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * UUID主键
   */
  @TableId(value = "hotel_room_allocation_id", type = IdType.INPUT)
  private String hotelRoomAllocationId;

  /**
   * 活动编号
   */
  @TableField(value = "activity_id")
  private String activityId;

  /**
   * 单位编号
   */
  @TableField(value = "activity_company_id")
  private String activityCompanyId;

  /**
   * 酒店编号
   */
  @TableField(value = "hotel_id")
  private String hotelId;

  /**
   * 用户编号
   */
  @TableField(value = "fair_user_id")
  private String fairUserId;

  @Excel(name = "活动名称")
  private String activityName;

  @Excel(name = "公司名称")
  private String companyName;

  @Excel(name = "酒店名称")
  private String hotelName;

  @Excel(name = "用户姓名")
  private String personName;

  /**
   * 用户性别
   */
  @Excel(name = "用户性别", dictType = "sys_user_sex")
  private String gender;

  @Excel(name = "手机号码")
  private String mobilePhone;

  @Excel(name = "证件号码")
  private String certNum;

  @Excel(name = "房型")
  private String roomType;

  /**
   * 房型编号
   */
  @TableField(value = "room_type_id")
  private String roomTypeId;

  /**
   * 是否与同公司人员同住
   */
  @Excel(name = "是否与同公司人员同住", dictType = "ei_yes_no")
  @TableField(value = "group_with_same_company")
  private Integer groupWithSameCompany;

  /**
   * 入住日期
   */
  @JsonFormat(pattern = "yyyy-MM-dd")
  @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd")
  @TableField(value = "check_in_date")
  private Date checkInDate;

  /**
   * 离开日期
   */
  @JsonFormat(pattern = "yyyy-MM-dd")
  @Excel(name = "离开日期", width = 30, dateFormat = "yyyy-MM-dd")
  @TableField(value = "check_out_date")
  private Date checkOutDate;

  /**
   * 分配状态，可选值：0=初始, 1=确认, -1=取消
   */
  @Excel(name = "分配状态", dictType = "ei_common_status")
  @TableField(value = "status")
  private Integer status;

  /**
   * 创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "create_dt")
  private Date createDt;

}
