package com.szhr.ei.converter.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 餐饮套餐信息对象 meal_option
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class MealOptionVO implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String mealOptionId;

    /** 关联活动ID */
    @Excel(name = "关联活动ID" )
    private String activityId;

    /** 餐饮类型，1=午餐盒饭,2=集体就餐 */
    @Excel(name = "餐饮类型", dictType = "ei_meal_type")
    private Integer mealType;

    /** 餐厅名称 */
    @Excel(name = "餐厅名称" )
    private String restaurantName;

    /** 餐厅地址 */
    @Excel(name = "餐厅地址" )
    private String address;

    /** 用餐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate mealDate;

    /** 就餐时间 */
    @JsonFormat(pattern = "HH:mm" )
    @Excel(name = "就餐时间", width = 30, dateFormat = "HH:mm" )
    private LocalTime mealTime;

    /** 餐厅图片URL */
    @Excel(name = "餐厅图片URL" )
    private String photoUrl;

    /** 最大用餐人数 */
    @Excel(name = "最大用餐人数" )
    private Integer maxPeople;

    /** 用户端选餐开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "用户端选餐开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date selectionStartTime;

    /** 用户端选餐结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "用户端选餐结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date selectionEndTime;

    /** 选项状态：0=待确认 1=已确认 -1=已取消 */
    @Excel(name = "选项状态", dictType = "ei_common_status")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 活动名称 */
    @Excel(name = "活动名称", width = 30)
    private String activityName;

}
