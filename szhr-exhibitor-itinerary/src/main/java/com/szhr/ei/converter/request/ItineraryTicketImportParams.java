package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 出行飞机或高铁班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ItineraryTicketImportParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** 旅途编号 */
    @TableId(value = "itinerary_ticket_id" , type = IdType.INPUT)
    private String itineraryTicketId;

    /** 活动ID */
    @Excel(name = "活动ID" )
    private String activityId;

    @Excel(name = "活动单位编号" )
    private String activityCompanyId;

    /** 参会用户编号 */
    @Excel(name = "参会用户编号" )
    private String fairUserId;

    @Excel(name = "姓名" )
    private String personName;

    @Excel(name = "性别" )
    private String gender;

    @Excel(name = "证件类型", dictType = "ei_cert_type_code")
    private String certType;

    @Excel(name = "证件号码" )
    private String certNum;

    @Excel(name = "手机号码" )
    private String mobilePhone;

    @Excel(name = "电子邮箱" )
    private String email;

    @Excel(name = "单位名称" )
    private String companyName;

    @Excel(name = "单位性质" )
    private String companyProp;

    /** 旅途方向，可选值：GOING, RETURN */
    @Excel(name = "旅途方向", dictType = "ei_travel_direction")
    private String ticketDirection;

    /** 出行方式，飞机、高铁、自驾 */
    @Excel(name = "出行方式", dictType = "ei_travel_mode")
    private String travelMode;

    /** 始发站 */
    @Excel(name = "始发站" )
    private String departureStation;

    /** 终点站 */
    @Excel(name = "终点站" )
    private String arrivalStation;

    /** 航班号/车次号 */
    @Excel(name = "航班号/车次号" )
    private String flightTrainNum;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date departureDt;

    /** 到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "到达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date arrivalDt;

    /** 座位号 */
    @Excel(name = "座位号" )
    private String seatNum;

    @Excel(name = "备注" )
    private String remarks;

    // 回程信息
    @Excel(name = "回程出行方式", dictType = "ei_travel_mode")
    private String travelModeBack;

    /** 回程始发站 */
    @Excel(name = "回程始发站" )
    private String departureStationBack;

    /** 终点站 */
    @Excel(name = "回程终点站" )
    private String arrivalStationBack;

    /** 航班号/车次号 */
    @Excel(name = "回程航班号/车次号" )
    private String flightTrainNumBack;

    /** 回程出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "回程出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date departureDtBack;

    /** 回程到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "回程到达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date arrivalDtBack;

    /** 回程座位号 */
    @Excel(name = "回程座位号" )
    private String seatNumBack;



}
