package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Date;
import java.time.LocalDate;

/**
 * 午饭盒饭选项对象 meal_box
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@TableName(value = "meal_box", autoResultMap = true)
public class MealBoxVO implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** 盒饭编号， UUID主键 */
    @TableId(value = "meal_box_id" , type = IdType.INPUT)
    private String mealBoxId;

    /** 餐饮套餐信息编号 */
    @Excel(name = "餐饮套餐信息编号" )
    @TableField(value = "meal_option_id")
    private String mealOptionId;

    /** 盒饭名称 */
    @Excel(name = "盒饭名称" )
    @TableField(value = "box_name")
    private String boxName;

    /** 盒饭描述 */
    @Excel(name = "盒饭描述" )
    @TableField(value = "description")
    private String description;

    /** 盒饭图片 */
    @Excel(name = "盒饭图片" )
    @TableField(value = "photo_url")
    private String photoUrl;

    /** 排序号 */
    @Excel(name = "排序号" )
    @TableField(value = "ranking")
    private Integer ranking;

    /** 活动名称 */
    @Excel(name = "活动名称", width = 30)
    private String activityName;

    /** 用餐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate mealDate;

    private Integer mealType;

    private String activityId;



}
