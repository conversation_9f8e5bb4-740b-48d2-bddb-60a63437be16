package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 房间分配记录对象 hotel_room_allocation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class HotelRoomAllocationQueryParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String hotelRoomAllocationId;

    /** 活动编号 */
    @Excel(name = "活动编号" )
    private String activityId;

    /** 单位编号 */
    @Excel(name = "单位编号" )
    private String activityCompanyId;

    /** 酒店编号 */
    @Excel(name = "酒店编号" )
    private String hotelId;

    /** 用户编号 */
    @Excel(name = "用户编号" )
    private String fairUserId;

    /** 房型编号 */
    @Excel(name = "房型编号" )
    private String roomTypeId;

    /** 是否与同公司人员同住 */
    @Excel(name = "是否与同公司人员同住" )
    private Integer groupWithSameCompany;

    /** 入住日期 */
    private Date checkInDate;

    /** 离开日期 */
    private Date checkOutDate;

    /** 分配状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "分配状态，可选值：0=初始, 1=确认, -1=取消" )
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 用户性别 */
    @Excel(name = "用户性别" )
    private String gender;

    private String personName;

    private String mobilePhone;

    private String activityName;

    private String companyName;

}
