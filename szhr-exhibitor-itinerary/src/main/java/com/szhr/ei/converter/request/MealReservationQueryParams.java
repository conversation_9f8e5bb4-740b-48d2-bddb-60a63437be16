package com.szhr.ei.converter.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户订餐记录对象 meal_reservation
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class MealReservationQueryParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String mealReservationId;

    /** 关联活动ID */
    @Excel(name = "关联活动ID" )
    private String activityId;

    /** 单位编号 */
    @Excel(name = "单位编号" )
    private String activityCompanyId;

    /** 用户ID */
    @Excel(name = "用户编号" )
    private String fairUserId;

    /** 用餐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private Date mealDate;

    /** 餐饮类型（1-午餐盒饭，2-聚餐） */
    @NotBlank(message = "餐饮类型不能为空")
    private Integer mealType;

    /** 餐饮选项ID */
    @Excel(name = "餐饮选项编号" )
    private String mealOptionId;

    /** 午饭盒饭编号 */
    @Excel(name = "午饭盒饭编号" )
    private String mealBoxId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 展位号 */
    @Excel(name = "展位号" )
    private String boothCode;

    /** 是否自行解决 */
    @Excel(name = "是否自行解决" )
    private Integer isSelfHelp;

    /** 预订状态：0-待确认 1-已确认 -1-已取消 */
    private Integer status;

    /** 备注 */
    private String remarks;

    private String personName;

    private String mobilePhone;

    private String activityName;


    private String companyName;


}
