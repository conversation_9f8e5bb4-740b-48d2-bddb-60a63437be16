package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.response.MealReservationExport;
import com.szhr.ei.converter.response.MealReservationVO;
import com.szhr.ei.domain.MealReservation;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface MealReservationConvert {
    MealReservationConvert INSTANCE = Mappers.getMapper(MealReservationConvert.class);

    public MealReservationVO toVo(MealReservation reservation);

    MealReservation toEntity(MealReservationVO vo);

    MealReservationExport toExport(MealReservationVO vo);

}
