package com.szhr.ei.converter.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 出行飞机或火车班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ItineraryTicketQueryParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** 旅途编号 */
    private String itineraryTicketId;

    /** 活动ID */
    @NotBlank(message = "活动ID不能为空")
    private String activityId;

    /** 参会单位编号 */
    private String activityCompanyId;

    /** 参会用户编号 */
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    @NotBlank(message = "出行方向不能为空")
    private String ticketDirection;

    /** 始发站 */
    private String departureStation;

    /** 终点站 */
    private String arrivalStation;

    /** 出行方式，飞机、高铁、自驾 */
    @NotBlank(message = "出行方式不能为空")
    private String travelMode;

    /** 出发时间 */
    private Date departureDt;

    /** 到达时间 */
    private Date arrivalDt;

    /** 飞机号/车次号 */
    private String flightTrainNum;

    /** 座位号 */
    private String seatNum;

    private String remarks;

    private String personName;

    private String mobilePhone;

    private String activityName;


    private String companyName;

    private Integer status;


}
