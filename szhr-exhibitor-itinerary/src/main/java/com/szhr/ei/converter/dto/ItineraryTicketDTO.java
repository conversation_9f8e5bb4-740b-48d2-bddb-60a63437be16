package com.szhr.ei.converter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 出行航班或火车班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ItineraryTicketDTO implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    /** 旅途编号 */
    private String itineraryTicketId;

    /** 活动ID */
    private String activityId;

    /** 参会用户编号 */
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    private String ticketDirection;

    /** 始发站 */
    private String departureStation;

    /** 终点站 */
    private String arrivalStation;

    /** 出行类型，飞机、高铁、自驾 */
    private String travelMode;

    /** 出发时间 */
    private Date departureDt;

    /** 到达时间 */
    private Date arrivalDt;

    /** 航班号/车次号 */
    private String flightTrainNum;

    /** 座位号 */
    private String seatNum;

    /** 预定状态，可选值：0=初始, 1=确认, -1=取消 */
    private Integer status;

    /** 创建时间 */
    private Date createDt;

    /** 更新时间 */
    private Date updateDt;

    /** 备注 */
    private String remarks;

    private String personName;
    private String mobilePhone;
    private String activityName;

}
