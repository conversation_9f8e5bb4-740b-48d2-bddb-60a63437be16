package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.request.ItineraryTicketImportParams;
import com.szhr.ei.converter.request.ItineraryTicketParams;
import com.szhr.ei.converter.response.ItineraryTicketVO;
import com.szhr.ei.domain.FairUser;
import com.szhr.ei.domain.ItineraryTicket;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItineraryTicketConvert {

    ItineraryTicketConvert INSTANCE = Mappers.getMapper(ItineraryTicketConvert.class);

    ItineraryTicketVO toVo(ItineraryTicket itineraryTicket);

    ItineraryTicket toPo(ItineraryTicketParams ItineraryTicketParams);

    ItineraryTicket toPo(ItineraryTicketImportParams itineraryTicketImportParams);

    FairUser toFairUser(ItineraryTicketImportParams itineraryTicketImportParams);

    @Mapping(source = "departureStationBack", target = "departureStation")
    @Mapping(source = "arrivalStationBack", target = "arrivalStation")
    @Mapping(source = "travelModeBack", target = "travelMode")
    @Mapping(source = "flightTrainNumBack", target = "flightTrainNum")
    @Mapping(source = "departureDtBack", target = "departureDt")
    @Mapping(source = "arrivalDtBack", target = "arrivalDt")
    @Mapping(source = "seatNumBack", target = "seatNum")
    ItineraryTicket toPoBack(ItineraryTicketImportParams itineraryTicketImportParams);

}
