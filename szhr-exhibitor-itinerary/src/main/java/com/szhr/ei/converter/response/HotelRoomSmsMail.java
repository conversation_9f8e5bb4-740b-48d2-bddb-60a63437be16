package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.ei.domain.HotelRoomAllocation;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 住宿分配短信和邮件
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
public class HotelRoomSmsMail extends HotelRoomAllocation implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    @TableField(value = "activity_name")
    private String activityName;

    @TableField(value = "company_name")
    private String companyName;

    @TableField(value = "hotel_name")
    private String hotelName;

    @TableField(value = "person_name")
    private String personName;

    @TableField(value = "gender")
    private String gender;

    @TableField(value = "mobile_phone")
    private String mobilePhone;

    @TableField(value = "cert_num")
    private String certNum;

    @TableField(value = "email")
    private String email;

    @TableField(value = "room_type")
    private String roomType;

}
