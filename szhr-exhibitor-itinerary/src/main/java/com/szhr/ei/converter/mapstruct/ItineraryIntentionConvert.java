package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.request.ItineraryIntentionModelParams;
import com.szhr.ei.converter.response.ItineraryIntentionVO;
import com.szhr.ei.domain.ItineraryIntention;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItineraryIntentionConvert {

    ItineraryIntentionConvert INSTANCE = Mappers.getMapper(ItineraryIntentionConvert.class);

    ItineraryIntentionVO toVo(ItineraryIntention itineraryIntention);

    @Mapping(target = "status", ignore = true)
    ItineraryIntention toPo(ItineraryIntentionModelParams itineraryIntentionModelParams);


}
