package com.szhr.ei.converter.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * 登记行程飞机高铁意向对象 itinerary_intention
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class ItineraryIntentionVO implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** 登记行程飞机高铁意向表编号 */
    private String itineraryIntentionId;

    /** 活动ID */
    @Excel(name = "活动ID" )
    private String activityId;

    /** 参会用户编号 */
    @Excel(name = "参会用户编号" )
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    @Excel(name = "旅途方向", dictType = "ei_travel_direction")
    private String ticketDirection;

    /** 出行方式，飞机、火车、自驾 */
    @Excel(name = "出行方式", dictType = "sys_travel_mode")
    private String travelMode;

    /** 客户选择出发日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "客户选择出发日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date departureDate;

    /** 客户选择出发时间段开始时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段开始时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime startTime;

    /** 客户选择出发时间段结束时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段结束时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime endTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 预订状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "预订状态", dictType = "ei_common_status")
    private Integer status;

    @Excel(name = "姓名" )
    private String personName;

    @Excel(name = "手机号" )
    private String mobilePhone;

    @Excel(name = "活动名称" )
    private String activityName;

    @Excel(name = "单位名称" )
    private String companyName;

}
