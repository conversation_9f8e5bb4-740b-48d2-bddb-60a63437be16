package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import lombok.Data;

/**
 * 报名单位信息对象 activity_company
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class ActivityCompanyParams{

    private String id;

    private String companyName;

    private String industry;

    private String companyType;

    private String companyProp;

    private String companyScale;

    private String contactPerson;

    private String mobilePhone;

    private String email;

    private String startDate;

    private String endDate;

}
