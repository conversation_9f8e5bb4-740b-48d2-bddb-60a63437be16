package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 邀约库对象 activity_invitation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class FairUserParams {
    private String activityId;

    private String activityCompanyId;

    private String companyName;

    private String personName;

    private String mobilePhone;

    private String certNum;

    private String email;
}
