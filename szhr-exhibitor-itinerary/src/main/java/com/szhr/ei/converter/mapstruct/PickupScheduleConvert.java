package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.response.PickupScheduleVO;
import com.szhr.ei.domain.PickupSchedule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface PickupScheduleConvert {
    PickupScheduleConvert INSTANCE = Mappers.getMapper(PickupScheduleConvert.class);
    PickupScheduleVO toVo(PickupSchedule pickupSchedule);

}
