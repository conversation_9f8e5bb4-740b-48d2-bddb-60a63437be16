package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Data
public class FairUserLoginVO {

    /**
     * 用户编号
     */
    private String fairUserId;

    /**
     * 活动编号
     */
    private String activityId;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 公司名称
     */
    private String activityCompanyId;


}
