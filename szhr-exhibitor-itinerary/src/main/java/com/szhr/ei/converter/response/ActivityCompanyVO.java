package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.domain.ActivityCompany;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityCompanyVO extends ActivityCompany {
    @Excel(name = "创建人用户名" )
    @TableField(value = "create_user_name")
    private String createUserName;

    @Excel(name = "所在行业", sort = 2, width = 30)
    @TableField(value = "industry_name")
    private String industryName;
}
