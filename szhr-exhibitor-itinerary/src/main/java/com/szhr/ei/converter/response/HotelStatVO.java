package com.szhr.ei.converter.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class HotelStatVO implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String id;

    /** 酒店名称 */
    private String name;

    /** 酒店目前已入住人数 */
    private Integer currNum;

    /** 酒店最大入住人数 */
    private Integer maxCapacity;

    /** 指定溢出酒店的唯一标识 */
    private String overflowHotelId;

}
