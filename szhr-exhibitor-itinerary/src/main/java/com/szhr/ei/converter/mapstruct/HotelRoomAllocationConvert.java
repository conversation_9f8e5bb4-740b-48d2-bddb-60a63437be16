package com.szhr.ei.converter.mapstruct;

import com.szhr.ei.converter.response.HotelRoomAllocationVO;
import com.szhr.ei.domain.HotelRoomAllocation;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface HotelRoomAllocationConvert {
    HotelRoomAllocationConvert INSTANCE = Mappers.getMapper(HotelRoomAllocationConvert.class);
    HotelRoomAllocationVO toVo(HotelRoomAllocation hotelRoomAllocation);

}
