package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.domain.Activity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityVO extends Activity {

    @Excel(name = "创建人用户名" )
    @TableField(value = "create_user_name")
    private String createUserName;

    @Excel(name = "创建人昵称" )
    @TableField(value = "create_nick_name")
    private String createNickName;
}
