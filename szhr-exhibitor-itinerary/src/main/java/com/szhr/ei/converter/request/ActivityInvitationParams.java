package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.szhr.common.annotation.Excel;
import com.szhr.common.utils.handler.mybatis.AESTypeHandler;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 邀约库对象 activity_invitation
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class ActivityInvitationParams{
    private String activityId;

    private String activityCompanyId;

    private String businessFeedback;

    private String aiFeedback;

    private Integer state;

    private Long calledBy;

    private Long createdBy;

    private Long managedBy;

    private Date callDt;

    private Integer callResultNumber;

    private String apiResult;

    private String companyName;

    private String industry;

    private String companyType;

    private String companyProp;

    private String companyScale;

    private String contactPerson;

    private String mobilePhone;

    private String email;
}
