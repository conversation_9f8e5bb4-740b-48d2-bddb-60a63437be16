package com.szhr.ei.converter.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 餐饮套餐信息对象 meal_option
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class MealOptionQueryParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String mealOptionId;

    /** 关联活动ID */
    private String activityId;

    /** 餐饮类型（1-午餐（盒饭），2-聚餐） */
    private Integer mealType;

    /** 餐厅名称 */
    private String restaurantName;

    /** 餐厅地址 */
    private String address;

    /** 用餐日期 */
    private LocalDate mealDate;

    /** 就餐时间 */
    private LocalTime mealTime;

    /** 最大用餐人数 */
    private Integer maxPeople;

    /** 用户端选餐开始时间 */
    private Date selectionStartTime;

    /** 用户端选餐结束时间 */
    private Date selectionEndTime;

    /** 选项状态：0=待确认 1=已确认 -1=已取消 */
    private Integer status;

    /** 创建时间 */
    private Date createDt;

    /** 活动名称 */
    private String activityName;

}
