package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.constant.ReadConverterExpConstants;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 出行航班或火车班次记录对象 itinerary_ticket
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ItineraryTicketVO implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    /** 旅途编号 */
    private String itineraryTicketId;

    /** 活动ID */
    private String activityId;

    /** 参会用户编号 */
    private String fairUserId;

    @Excel(name = "姓名" )
    private String personName;

    @Excel(name = "手机号" )
    private String mobilePhone;

    @Excel(name = "活动名称" )
    private String activityName;

    @Excel(name = "单位名称", width = 50)
    private String companyName;

    /** 参会单位编号 */
    @Excel(name = "单位编号", width = 40)
    private String activityCompanyId;

    /** 旅途方向，可选值：GOING, RETURN */
    //@Excel(name = "旅途方向", readConverterExp = ReadConverterExpConstants.TICKET_DIRECTION)
    private String ticketDirection;

    /** 始发站 */
    @Excel(name = "始发站" )
    private String departureStation;

    /** 终点站 */
    @Excel(name = "终点站" )
    private String arrivalStation;

    /** 出行类型，飞机、火车、自驾 */
    //@Excel(name = "出行类型", readConverterExp = ReadConverterExpConstants.TRAVEL_MODE)
    @Excel(name = "出行类型")
    private String travelMode;

    /** 出发时间 */
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date departureDt;

    /** 到达时间 */
    @Excel(name = "到达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date arrivalDt;

    /** 航班号/车次号 */
    @Excel(name = "航班号/车次号" )
    private String flightTrainNum;

    /** 座位号 */
    @Excel(name = "座位号" )
    private String seatNum;

    /** 预定状态，可选值：0=初始, 1=确认, -1=取消 */
    @Excel(name = "预定状态", dictType = "ei_common_status")
    private Integer status;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date updateDt;

    /** 备注 */
    @Excel(name = "备注", width = 30 )
    private String remarks;




}
