package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.ei.domain.ActivityAttachment;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityAttachmentVO extends ActivityAttachment {

    @TableField(value = "create_user_name")
    private String createUserName;

    @TableField(value = "file_extension")
    private String fileExtension;
}
