package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import com.szhr.ei.constant.ReadConverterExpConstants;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 用户订餐记录对象 meal_reservation
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class MealReservationVO implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String mealReservationId;

    /** 关联活动ID */
    @Excel(name = "关联活动编号" )
    private String activityId;

    /** 用户ID */
    @Excel(name = "用户编号" )
    private String fairUserId;

    @Excel(name = "姓名" )
    private String personName;

    @Excel(name = "手机号" )
    private String mobilePhone;

    @Excel(name = "活动名称" )
    private String activityName;

    @Excel(name = "单位名称", width = 50)
    private String companyName;

    @Excel(name = "单位编号", width = 40)
    private String activityCompanyId;

    /** 餐饮选项ID */
    @NotBlank(message = "餐饮选项编号不能为空")
    @Excel(name = "餐饮选项编号" )
    private String mealOptionId;

    /** 午饭盒饭编号 */
    @Excel(name = "午饭盒饭编号" )
    private String mealBoxId;

    /** 用餐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd" )
    private LocalDate mealDate;

    /** 就餐时间 */
    @Excel(name = "就餐时间", width = 30, dateFormat = "HH:mm:ss")
    private LocalTime mealTime;

    /** 餐厅名称 */
    @Excel(name = "餐厅名称" )
    private String restaurantName;

    /** 餐饮类型1-午餐盒饭，2-聚餐 */
    @Excel(name = "餐饮类型" , dictType = "ei_meal_type")
    private Integer mealType;

    /** 午餐套餐名称 */
    @Excel(name = "午餐套餐名称")
    private String boxName;

    /** 展位号 */
    private String boothCode;

    /** 是否自行解决 */
    @Excel(name = "是否自行解决", dictType = "ei_yes_no")
    private Integer isSelfHelp;

    /** 预订状态：0-待确认 1-已确认 -1-已取消 */
    @Excel(name = "预订状态", dictType = "ei_common_status")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;

    /** 备注 */
    @Excel(name = "备注" )
    private String remarks;



}
