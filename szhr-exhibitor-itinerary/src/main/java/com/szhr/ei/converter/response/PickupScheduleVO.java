package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 接送安排对象 pickup_schedule
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class PickupScheduleVO implements Serializable {
    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String id;

    /** 活动ID */
    @Excel(name = "活动ID" )
    private String activityId;

    /** 司机姓名 */
    @Excel(name = "司机姓名" )
    private String driverName;

    /** 司机联系电话 */
    @Excel(name = "司机联系电话" )
    private String driverPhone;

    /** 车牌号 */
    @Excel(name = "车牌号" )
    private String licensePlate;

    /** 出发地 */
    @Excel(name = "出发地" )
    private String departureLocation;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd" )
    private Date departureTime;

    /** 目的地 */
    @Excel(name = "目的地" )
    private String destination;

    /** 工作人员姓名 */
    @Excel(name = "工作人员姓名" )
    private String staffName;

    /** 工作人员电话 */
    @Excel(name = "工作人员电话" )
    private String staffPhone;

    /** 活动名称 */
    @Excel(name = "活动名称" )
    private String activityName;

}
