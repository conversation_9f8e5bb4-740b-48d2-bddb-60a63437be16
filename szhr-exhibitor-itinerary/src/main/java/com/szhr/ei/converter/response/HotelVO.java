package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class HotelVO implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    private String hotelId;

    /** 关联活动ID */
    private String activityId;

    /** 关联活动名称 */
    private String activityName;

    /** 酒店名称 */
    private String name;

    /** 酒店目前已入住人数 */
    private Integer currNum;

    /** 酒店最大入住人数 */
    private Integer maxCapacity;

    /** 酒店地址 */
    private String address;

    /** 是否为备用酒店 */
    private Boolean backup;

    /** 创建时间 */
    private Date createDt;

    /** 前台电话 */
    private String frontDeskPhone;

    /** 酒店图片 */
    private String imgUrl;

    /** 指定溢出酒店的唯一标识 */
    private String overflowHotelId;

    /** 状态，可选值：0=初始, 1=确认, -1=取消 */
    private Integer status;

}
