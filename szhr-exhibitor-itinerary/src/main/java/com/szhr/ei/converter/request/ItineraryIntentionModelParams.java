package com.szhr.ei.converter.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * 登记行程飞机高铁意向对象Model itinerary_intention
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ItineraryIntentionModelParams implements Serializable {
    @Serial
    private static final long serialVersionUID=1L;

    /** 登记行程飞机高铁意向记录编号 */
    private String itineraryIntentionId;

    /** 活动ID */
    @NotBlank(message = "活动ID不能为空")
    @Excel(name = "活动ID" )
    private String activityId;

    /** 参会用户编号 */
    @Excel(name = "参会用户编号" )
    private String fairUserId;

    /** 旅途方向，可选值：GOING, RETURN */
    @NotBlank(message = "旅途方向不能为空")
    @Excel(name = "旅途方向，可选值：GOING, RETURN" )
    private String ticketDirection;

    /** 出行方式，飞机、火车、自驾 */
    @NotBlank(message = "出行方式不能为空")
    @Excel(name = "出行方式，飞机、火车、自驾" )
    private String travelMode;

    /** 客户选择出发日期 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "客户选择出发日期", width = 30, dateFormat = "yyyy-MM-dd" )
    private Date departureDate;

    /** 客户选择出发时间段开始时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段开始时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime startTime;

    /** 客户选择出发时间段结束时间，格式HH:mm */
    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "客户选择出发时间段结束时间，格式HH:mm", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime endTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date createDt;




}
