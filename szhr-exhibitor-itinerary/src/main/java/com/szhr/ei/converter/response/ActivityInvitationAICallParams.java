package com.szhr.ei.converter.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ActivityInvitationAICallParams {

    private String activityId;

    private String phone;

    private Integer state;

    /** 外呼操作人 */
    private Long calledBy;

    /** 外呼时间 */
    private Date callDt;

    /** 呼叫结果（0：未接通，1：已接通） */
    private Integer callResultNumber;

    /** 外呼api响应结果 */
    private String apiResult;

}
