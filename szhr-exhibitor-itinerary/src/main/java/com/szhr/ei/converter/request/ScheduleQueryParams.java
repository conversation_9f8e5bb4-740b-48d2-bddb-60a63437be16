package com.szhr.ei.converter.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.szhr.common.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.time.LocalTime;
import java.util.Date;

@Data
public class ScheduleQueryParams {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /** UUID主键 */
    @TableId(value = "schedule_id" , type = IdType.INPUT)
    private String scheduleId;

    /** 关联活动ID */
    @Excel(name = "关联活动ID" )
    @TableField(value = "activity_id")
    private String activityId;

    /** 出发地 */
    @Excel(name = "出发地" )
    @TableField(value = "departure_address")
    private String departureAddress;

    /** 目的地 */
    @Excel(name = "目的地" )
    @TableField(value = "arrival_address")
    private String arrivalAddress;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm" )
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm" )
    @TableField(value = "schedule_date")
    private Date scheduleDate;

    /** 司机姓名 */
    @Excel(name = "司机姓名" )
    @TableField(value = "driver_name")
    private String driverName;

    /** 司机电话 */
    @Excel(name = "司机电话" )
    @TableField(value = "driver_number")
    private String driverNumber;

    /** 工作人员姓名 */
    @Excel(name = "工作人员姓名" )
    @TableField(value = "staff_name")
    private String staffName;

    /** 工作人员电话 */
    @Excel(name = "工作人员电话" )
    @TableField(value = "staff_number")
    private String staffNumber;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd" )
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd" )
    @TableField(value = "created_at")
    private Date createdAt;

    @JsonFormat(pattern = "HH:mm:ss" )
    @Excel(name = "出发时间", width = 30, dateFormat = "HH:mm:ss" )
    private LocalTime scheduleTime;

    private String carNumber;

}
