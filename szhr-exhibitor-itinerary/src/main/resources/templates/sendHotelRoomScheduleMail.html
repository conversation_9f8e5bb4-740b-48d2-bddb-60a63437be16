<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <title>酒店房间及日程安排通知</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background-color: #f8f8f8;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid #e7e7e7;
      }
      .content {
        padding: 20px;
      }
      p {
        font-size: 16px;
      }
      .footer {
        margin-top: 20px;
        padding-top: 10px;
        border-top: 1px solid #e7e7e7;
        font-size: 12px;
        color: #777;
      }
      pre {
        font-family: Arial, sans-serif;
        font-size: 14px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
      }
      table, th, td {
        border: 1px solid #ddd;
      }
      th, td {
        padding: 10px;
        text-align: left;
      }
      th {
        background-color: #f2f2f2;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h2>酒店房间及日程安排通知</h2>
    </div>

    <div class="content">
      <p>各位参会代表大家好，以下为重要通知：</p>
      <h3>酒店房间信息：</h3>

      <ul>
        <li>入住人姓名：<span th:text="${hotelRoomSmsMail.personName}">姓名</span></li>
        <li>参与活动名称：<span th:text="${hotelRoomSmsMail.activityName}">活动名</span></li>
        <li>参与公司名称：<span th:text="${hotelRoomSmsMail.companyName}">公司名</span></li>
        <li>酒店名称：<span th:text="${hotelRoomSmsMail.hotelName}">酒店</span></li>
        <li>房型：<span th:text="${hotelRoomSmsMail.roomType}">房型</span></li>
        <li>是否与同公司人员同住：
          <span th:text="${hotelRoomSmsMail.groupWithSameCompany == 1} ? '是' : '否'">是/否</span>
        </li>
        <li>房间预定日期：
          <span th:text="${#dates.format(hotelRoomSmsMail.reserveDate, 'yyyy-MM-dd')}">2025-01-01</span>
        </li>
        <li>分配状态：
          <span th:switch="${hotelRoomSmsMail.status}">
                <span th:case="1">确认</span>
                <span th:case="-1">取消</span>
                <span th:case="*">初始</span>
            </span>
        </li>
      </ul>
      <hr/>

      <div th:each="schedule, iterStat : ${schedules}">
        <h3>日程<span th:text="${iterStat.count}">1</span></h3>
        <ul>
          <li>出发日期：
            <span th:text="${#dates.format(schedule.scheduleDate, 'yyyy-MM-dd')}">2025-01-01</span>
          </li>
          <li>出发地：<span th:text="${schedule.departureAddress}">出发地</span></li>
          <li>目的地：<span th:text="${schedule.arrivalAddress}">目的地</span></li>
          <li>司机姓名：<span th:text="${schedule.driverName}">司机姓名</span></li>
          <li>司机电话：<span th:text="${schedule.driverNumber}">司机电话</span></li>
          <li>工作人员姓名：<span th:text="${schedule.staffName}">工作人员姓名</span></li>
          <li>工作人员电话：<span th:text="${schedule.staffNumber}">工作人员电话</span></li>
        </ul>
      </div>

      <pre>
        ==============以下为模板样例，仅供参考==============
            ⏰ 出发时间/地点：2024年4月13日（星期日） 郁锦香酒店和西郊酒店的一楼
            班车车次时间：早上 8:00  出发。
            酒店集合时间：早上7:50
            📍 活动地点：国家会展中心（上海）4.2号馆22号门
            🚩过安检后，企业签到处：8:00-8:30 排队领取参展证、餐票（每家企业限2张)。

            🚇 地铁指引：乘坐2号线至国家会展中心站，5号出口。
            🚗 打车导航：请定位国家会展中心6号门，按现场指示入场。

            报道流程：
            1.二维码提前预约/现场扫码预约后，凭身份证及预约码完成身份核验后方可入场。
            2.若未携带身份证现场需要个人姓名+身份证号码核验通过后入场。
            3.第一次完成扫码预约并成功入场的企业参展人员，在有效期内均可刷身份证入场，无需多次预约。

            📋会务服务团队及联系方式
            现场咨询联系人：郭女士 13192261360 ； 陶先生 13724375577 ； 胡先生 15099920294
            接驳车辆：周女士 13923745607
            企业餐饮：马女士 13662230430
            安全保卫：赵先生 15875594226

            📋 注意事项
            1. 活动内容涉及重要安排，请务必准时到场；
            2. 现场请保持通讯设备静音，遵守会场秩序。

            感谢您的支持与配合！期待与您共聚盛会！
            ✨ 温馨提示：会展中心内设有指引牌，抵达后请留意工作人员引导。 谢谢！！！！  入群请备注企业名称，感谢🌹
            [文件]参展企业手册0410-v2.pdf
    </pre>

    </div>

  </body>
</html>
