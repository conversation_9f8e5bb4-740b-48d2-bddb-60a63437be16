<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ScheduleMapper">
    <resultMap type="com.szhr.ei.domain.Schedule" id="ScheduleResult">
        <result property="scheduleId"  column="schedule_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="departureAddress"  column="departure_address"/>
        <result property="arrivalAddress"  column="arrival_address"/>
        <result property="scheduleDate"  column="schedule_date"/>
        <result property="scheduleTime"  column="schedule_time"/>
        <result property="driverName"  column="driver_name"/>
        <result property="driverNumber"  column="driver_number"/>
        <result property="staffName"  column="staff_name"/>
        <result property="staffNumber"  column="staff_number"/>
        <result property="carNumber"  column="car_number"/>
        <result property="createdAt"  column="created_at"/>
    </resultMap>



    <select id="selectScheduleList" resultType="com.szhr.ei.domain.Schedule">
        SELECT
        schedule_id,
        activity_id,
        departure_address,
        arrival_address,
        schedule_date,
        schedule_time,
        driver_name,
        driver_number,
        staff_name,
        staff_number,
        car_number,
        created_at
        FROM schedule
        <where>
            <if test="scheduleId != null and scheduleId != ''">
                AND schedule_id = #{scheduleId}
            </if>
            <if test="activityId != null and activityId != ''">
                AND activity_id = #{activityId}
            </if>
            <if test="scheduleDate != null">
                AND DATE(schedule_date) = #{scheduleDate}
            </if>
            <if test="scheduleTime != null">
                AND TIME(schedule_time) = #{scheduleTime}
            </if>
            <if test="departureAddress != null and departureAddress != ''">
                AND departure_address = #{departureAddress}
            </if>
            <if test="arrivalAddress != null and arrivalAddress != ''">
                AND arrival_address = #{arrivalAddress}
            </if>
            <if test="driverName != null and driverName != ''">
                AND driver_name = #{driverName}
            </if>
            <if test="driverNumber != null and driverNumber != ''">
                AND driver_number = #{driverNumber}
            </if>
            <if test="staffName != null and staffName != ''">
                AND staff_name = #{staffName}
            </if>
            <if test="staffNumber != null and staffNumber != ''">
                AND staff_number = #{staffNumber}
            </if>
            <if test="carNumber != null and carNumber != ''">
                AND car_number = #{carNumber}
            </if>
        </where>
    </select>



</mapper>
