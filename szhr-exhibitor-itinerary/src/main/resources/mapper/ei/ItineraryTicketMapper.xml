<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ItineraryTicketMapper">
    <resultMap type="com.szhr.ei.domain.ItineraryTicket" id="ItineraryTicketResult">
        <result property="itineraryTicketId"  column="itinerary_ticket_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="fairUserId"  column="fair_user_id"/>
        <result property="ticketDirection"  column="ticket_direction"/>
        <result property="departureStation"  column="departure_station"/>
        <result property="arrivalStation"  column="arrival_station"/>
        <result property="travelMode"  column="travel_mode"/>
        <result property="departureDt"  column="departure_dt"/>
        <result property="arrivalDt"  column="arrival_dt"/>
        <result property="flightTrainNum"  column="flight_train_num"/>
        <result property="seatNum"  column="seat_num"/>
        <result property="createDt"  column="create_dt"/>
        <result property="updateDt"  column="update_dt"/>
        <result property="remarks"  column="remarks"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.ItineraryTicketVO" id="ItineraryTicketVOResult" extends="ItineraryTicketResult">
        <result property="companyName" column="company_name" />
        <result property="activityName" column="activity_name" />
        <result property="personName" column="person_name" />
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
    </resultMap>

    <select id="selectItineraryTicketVOList" parameterType="com.szhr.ei.converter.request.ItineraryTicketParams" resultMap="ItineraryTicketVOResult">
        select aa.*, bb.company_name
        from
        (
        select it.*, a.activity_name, fu.person_name , fu.mobile_phone
        from itinerary_ticket it
        left join activity a on it.activity_id = a.id
        left join fair_user fu on it.fair_user_id = fu.fair_user_id
        <where>
            <if test="status != null"> and it.status = #{status} </if>
            <if test="activityId != null and activityId != ''"> and it.activity_id = #{activityId} </if>
            <if test="activityCompanyId != null and activityCompanyId != ''"> and it.activity_company_id = #{activityCompanyId} </if>
            <if test="ticketDirection != null and ticketDirection != ''"> and it.ticket_direction = #{ticketDirection} </if>
            <if test="activityName != null and activityName != ''"> and a.activity_name like concat('%', #{activityName}, '%') </if>
            <if test="personName != null and personName != ''"> and fu.person_name like concat('%', #{personName}, '%') </if>
            <if test="mobilePhone != null and mobilePhone != ''"> and fu.mobile_phone like concat('%', #{mobilePhone}, '%')</if>
            <if test="fairUserId != null and fairUserId != ''"> and fu.fair_user_id = #{fairUserId} </if>
        </where>
        order by a.create_dt desc
        ) aa
        left join
        (
        select ai.activity_id, max(ai.activity_company_id) as activity_company_id, max(c.company_name) as company_name
        from (
        select activity_id, activity_company_id
        from activity_invitation
        where state = 2 and delete_dt is null
        ) ai
        left join company c on ai.activity_company_id = c.company_id
        group by ai.activity_id
        ) bb
        on aa.activity_id = bb.activity_id
        <where>
            <if test="companyName != null and companyName != ''"> and bb.company_name like concat('%', #{companyName}, '%') </if>
        </where>
    </select>


</mapper>
