<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.HotelMapper">
    <resultMap type="com.szhr.ei.domain.Hotel" id="HotelResult">
        <result property="hotelId"  column="hotel_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="name"  column="name"/>
        <result property="currNum"  column="curr_num"/>
        <result property="maxCapacity"  column="max_capacity"/>
        <result property="address"  column="address"/>
        <result property="backup"  column="backup"/>
        <result property="createDt"  column="create_dt"/>
        <result property="frontDeskPhone"  column="front_desk_phone"/>
        <result property="imgUrl"  column="img_url" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.HotelStatVO" id="HotelStatVOResult">
        <result property="hotelId" column="hotel_id" />
        <result property="name" column="name" />
        <result property="currNum" column="curr_num" />
        <result property="maxCapacity" column="max_capacity" />
    </resultMap>

    <select id="queryHotelStatVOList" parameterType="com.szhr.ei.domain.Hotel" resultMap="HotelStatVOResult">
        select h.hotel_id, h.name, hra.curr_num, h.max_capacity
        from hotel h
        left join activity a on h.activity_id = a.id
        left join
        (select hotel_id, count(1) as curr_num from hotel_room_allocation t  group by t.hotel_id) as hra
        on h.hotel_id = hra.hotel_id
        <where>
            <if test="activityId != null and activityId != ''"> h.activity_id = #{activityId} </if>
        </where>
    </select>
</mapper>
