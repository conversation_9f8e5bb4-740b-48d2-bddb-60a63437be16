<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.MealBoxMapper">
    <resultMap type="com.szhr.ei.domain.MealBox" id="MealBoxResult">
        <result property="mealBoxId"  column="meal_box_id"/>
        <result property="mealOptionId"  column="meal_option_id"/>
        <result property="boxName"  column="box_name"/>
        <result property="description"  column="description"/>
        <result property="photoUrl"  column="photo_url" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler"/>
        <result property="ranking"  column="ranking"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.MealBoxVO" id="MealBoxVOResult" extends="MealBoxResult">
        <result property="activityId" column="activity_id" />
        <result property="activityName" column="activity_name" />
        <result property="mealType" column="meal_type" />
        <result property="mealDate" column="meal_date" />
    </resultMap>

    <select id="selectVOList" parameterType="com.szhr.ei.converter.request.MealBoxQueryParams" resultMap="MealBoxVOResult">
        select aa.*, a.activity_name from
            (select mb.*, mo.activity_id, mo.meal_type, mo.meal_date, mo.status
            from meal_box mb
            left join meal_option mo on mb.meal_option_id = mo.meal_option_id) aa
        left join activity a on aa.activity_id = a.id
        <where>
            a.delete_dt is null
            <if test="activityName != null  and activityName != ''">
                and a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="mealDate != null"> and DATE_FORMAT(aa.meal_date, '%y-%m-%d') = DATE_FORMAT(#{mealDate}, '%y-%m-%d') </if>
            <if test="mealType != null"> and aa.meal_type = #{mealType} </if>
            <if test="status != null"> and aa.status = #{status}  </if>
            <if test="activityId != null and activityId != ''"> and aa.activity_id = #{activityId} </if>
        </where>
        order by aa.ranking desc
    </select>
</mapper>
