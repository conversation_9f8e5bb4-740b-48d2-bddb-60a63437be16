<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.PickupScheduleMapper">
    <resultMap type="com.szhr.ei.domain.PickupSchedule" id="PickupScheduleResult">
        <result property="id"  column="id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="driverName"  column="driver_name"/>
        <result property="driverPhone"  column="driver_phone"/>
        <result property="licensePlate"  column="license_plate"/>
        <result property="departureLocation"  column="departure_location"/>
        <result property="departureTime"  column="departure_time"/>
        <result property="destination"  column="destination"/>
        <result property="staffName"  column="staff_name"/>
        <result property="staffPhone"  column="staff_phone"/>
    </resultMap>

    <!-- 查询指定日期的接送安排（返回PickupScheduleVO，只查询实际存在的字段） -->
    <select id="selectPickupScheduleVOsByDate" resultMap="PickupScheduleResult">
        SELECT
            ps.id,
            ps.activity_id,
            a.activity_name,
            ps.driver_name,
            ps.driver_phone,
            ps.license_plate,
            ps.departure_location,
            ps.departure_time,
            ps.destination,
            ps.staff_name,
            ps.staff_phone
        FROM pickup_schedule ps
                 LEFT JOIN activity a ON ps.activity_id = a.id
        WHERE ps.activity_id = #{activityId}
          AND DATE(ps.departure_time) = #{scheduleDate}
        ORDER BY ps.departure_time ASC
    </select>
</mapper>
