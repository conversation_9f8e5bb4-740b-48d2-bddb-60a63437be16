<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ActivityAttachmentMapper">
    <resultMap type="com.szhr.ei.domain.ActivityAttachment" id="ActivityAttachmentResult">
        <result property="id"  column="id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="attachName"  column="attach_name"/>
        <result property="attachCategory"  column="attach_category"/>
        <result property="linkUrl"  column="link_url" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler"/>
        <result property="createdBy"  column="created_by"/>
        <result property="createDt"  column="create_dt"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.ActivityAttachmentVO" id="QueryResult" extends="com.szhr.ei.mapper.ActivityAttachmentMapper.ActivityAttachmentResult">
        <result property="createUserName"    column="create_user_name"    />
        <result property="fileExtension"    column="file_extension"    />
    </resultMap>

    <select id="selectActivityAttachmentList" resultMap="QueryResult">
        SELECT a.id, a.activity_id, a.attach_name, a.attach_category, a.link_url, a.created_by, a.create_dt,
               su.user_name as create_user_name,
               SUBSTRING_INDEX(link_url, '.', -1) AS file_extension
        FROM
            activity_attachment a
        LEFT JOIN
            sys_user su ON su.user_id = a.created_by
        <where>
            a.activity_id = #{activityId}
        </where>
        ORDER BY a.create_dt DESC
    </select>


</mapper>
