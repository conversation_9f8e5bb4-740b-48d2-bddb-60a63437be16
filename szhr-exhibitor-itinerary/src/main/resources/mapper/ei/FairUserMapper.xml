<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.FairUserMapper">
    <resultMap type="com.szhr.ei.domain.FairUser" id="FairUserResult">
        <result property="fairUserId"  column="fair_user_id"/>
        <result property="personName"  column="person_name"/>
        <result property="gender"  column="gender"/>
        <result property="mobilePhone"  column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="certTypeCode"  column="cert_type_code"/>
        <result property="certNum"  column="cert_num" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="email"  column="email" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="activityId"  column="activity_id"/>
        <result property="activityCompanyId"  column="activity_company_id"/>
        <result property="createdBy"  column="created_by"/>
        <result property="createDt"  column="create_dt"/>
        <result property="lastUpdatedBy"  column="last_updated_by"/>
        <result property="lastUpdateDt"  column="last_update_dt"/>
        <result property="lastLoginDt"  column="last_login_dt"/>
        <result property="deleteDt"  column="delete_dt"/>
        <result property="deletedBy"  column="deleted_by"/>
    </resultMap>

    <resultMap id="QueryResult" type="com.szhr.ei.converter.response.FairUserVO" extends="FairUserResult">
        <result property="companyName"  column="company_name"/>
        <result property="companyType"  column="company_type"/>
        <result property="createUserName"    column="create_user_name"/>
    </resultMap>
    <select id="selectFairUserList" resultMap="QueryResult">
        select
            a.fair_user_id,
            a.person_name,
            a.gender,
            a.mobile_phone,
            a.cert_type_code,
            a.cert_num,
            a.activity_id,
            a.activity_company_id,
            a.created_by,
            a.create_dt,
            a.last_updated_by,
            a.last_update_dt,
            a.last_login_dt,
            ac.company_name,
            ac.company_type,
            su.user_name as create_user_name
        from fair_user a
        left join activity_company ac ON ac.id = a.activity_company_id
        left join sys_user su ON su.user_id = a.created_by
        where
        a.delete_dt is null
        <if test="activityId != null and activityId != ''">
            and a.activity_id = #{activityId}
        </if>
        <if test="activityCompanyId != null and activityCompanyId != ''">
            and a.activity_company_id = #{activityCompanyId}
        </if>
        <if test="mobilePhone != null and mobilePhone != ''">
            and a.mobile_phone = #{mobilePhone, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
        </if>
        <if test="certNum != null and certNum != ''">
            and a.cert_num = #{certNum, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
        </if>
        <if test="personName != null and personName != ''">
            and a.person_name like concat('%',#{personName},'%')
        </if>
        <if test="companyName != null and companyName != ''">
            and ac.company_name like concat('%',#{companyName},'%')
        </if>
        order by a.create_dt desc
    </select>
</mapper>
