<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.HotelRoomAllocationMapper">
    <!-- 酒店房间分配结果映射（正确映射hotel_room_allocation_id） -->
    <resultMap type="com.szhr.ei.domain.HotelRoomAllocation" id="HotelRoomAllocationResult">
        <id property="hotelRoomAllocationId" column="hotel_room_allocation_id"/>
        <result property="hotelRoomAllocationId" column="hotel_room_allocation_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="activityCompanyId" column="activity_company_id"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="fairUserId" column="fair_user_id"/>
        <result property="roomTypeId" column="room_type_id"/>
        <result property="groupWithSameCompany" column="group_with_same_company"/>
        <result property="checkInDate" column="check_in_date"/>
        <result property="checkOutDate" column="check_out_date"/>
        <result property="status" column="status"/>
        <result property="createDt" column="create_dt"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.HotelRoomAllocationExport" id="HotelRoomAllocationExportResult" extends="HotelRoomAllocationResult">
        <result property="activityName" column="activity_name" />
        <result property="companyName" column="company_name" />
        <result property="hotelName" column="hotel_name" />
        <result property="personName" column="person_name" />
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="certNum" column="cert_num" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="roomType" column="room_type" />
        <result property="gender"  column="gender"/>
    </resultMap>

    <select id="selectHotelRoomAllocationExportList" parameterType="com.szhr.ei.converter.request.HotelRoomAllocationQueryParams" resultMap="HotelRoomAllocationExportResult">
        SELECT
        a.activity_name,
        ac.company_name,
        h.name hotel_name,
        fu.person_name,fu.mobile_phone,fu.gender,fu.cert_num,
        rt.name room_type,
        hra.*
        FROM hotel_room_allocation hra
        LEFT JOIN activity a ON hra.activity_id = a.id
        LEFT JOIN activity_company ac ON hra.activity_company_id = ac.id
        LEFT JOIN hotel h ON hra.hotel_id = h.hotel_id
        LEFT JOIN fair_user fu ON hra.fair_user_id = fu.fair_user_id
        LEFT JOIN room_type rt ON hra.room_type_id = rt.id
        <where>
            <if test="status != null"> and hra.status = #{status} </if>
            <if test="activityId != null and activityId != ''"> and hra.activity_id = #{activityId} </if>
            <if test="activityName != null and activityName != ''"> and a.activity_name like concat('%', #{activityName}, '%') </if>
            <if test="activityCompanyId != null and activityCompanyId != ''"> and hra.activity_company_id = #{activityCompanyId} </if>
            <if test="companyName != null and companyName != ''"> and ac.company_name like concat('%', #{companyName}, '%') </if>
            <if test="fairUserId != null and fairUserId != ''"> and fu.fair_user_id = {fairUserId} </if>
            <if test="personName != null and personName != ''"> and fu.person_name like concat('%', #{personName}, '%') </if>
            <if test="mobilePhone != null and mobilePhone != ''"> and fu.mobile_phone = #{mobilePhone, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler} </if>
        </where>
        ORDER BY a.activity_name, ac.company_name,h.`name`, fu.gender,fu.person_name
    </select>

    <resultMap type="com.szhr.ei.converter.response.HotelRoomSmsMail" id="HotelRoomSmsMailResult" extends="HotelRoomAllocationResult">
        <result property="activityName" column="activity_name" />
        <result property="companyName" column="company_name" />
        <result property="hotelName" column="hotel_name" />
        <result property="personName" column="person_name" />
        <result property="gender" column="gender" />
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler" />
        <result property="certNum" column="cert_num" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler" />
        <result property="email" column="email" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler" />
        <result property="roomType" column="room_type" />
    </resultMap>

    <select id="getHotelRoomSmsMailById" parameterType="String" resultMap="HotelRoomSmsMailResult">
        SELECT
        a.activity_name,
        ac.company_name,
        h.name hotel_name,
        fu.person_name,fu.mobile_phone,fu.gender,fu.cert_num,fu.email,
        rt.name room_type,
        hra.*
        FROM hotel_room_allocation hra
        LEFT JOIN activity a ON hra.activity_id = a.id
        LEFT JOIN activity_company ac ON hra.activity_company_id = ac.id
        LEFT JOIN hotel h ON hra.hotel_id = h.hotel_id
        LEFT JOIN fair_user fu ON hra.fair_user_id = fu.fair_user_id
        LEFT JOIN room_type rt ON hra.room_type_id = rt.id
        <where>
            <if test="hotelRoomAllocationId != null and hotelRoomAllocationId != ''"> and hra.hotel_room_allocation_id = #{hotelRoomAllocationId} </if>
        </where>
    </select>


    <resultMap id="HotelRoomAllocationResponceResult" type="com.szhr.ei.converter.response.HotelRoomAllocationResponce">
        <result property="hotelName" column="hotel_name"/>
        <result property="hotelAddress" column="hotel_address"/>
        <result property="frontDeskPhone" column="front_desk_phone"/>
        <result property="imgUrl" column="img_url"/>
        <result property="roomType" column="room_type"/>
        <result property="fairUserName" column="fair_user_name"/>
        <result property="checkInDate" column="check_in_date"/>
        <result property="checkOutDate" column="check_out_date"/>
    </resultMap>

    <select id="selectHotelInformationList" parameterType="com.szhr.ei.domain.HotelRoomAllocation" resultMap="HotelRoomAllocationResponceResult">
        SELECT
        h.name AS hotel_name,
        h.address AS hotel_address,
        h.front_desk_phone,
        h.img_url,
        rt.name AS room_type,
        fu.person_name AS fair_user_name,
        hra.check_in_date AS check_in_date,
        hra.check_out_date AS check_out_date
        FROM hotel_room_allocation hra
        LEFT JOIN hotel h ON hra.hotel_id = h.hotel_id
        LEFT JOIN room_type rt ON hra.room_type_id = rt.id
        LEFT JOIN fair_user fu ON hra.fair_user_id = fu.fair_user_id
        <where>
            <if test="fairUserId != null and fairUserId != ''"> AND hra.fair_user_id = #{fairUserId} </if>
            <if test="activityId != null and activityId != ''"> AND hra.activity_id = #{activityId} </if>
        </where>
    </select>
</mapper>

