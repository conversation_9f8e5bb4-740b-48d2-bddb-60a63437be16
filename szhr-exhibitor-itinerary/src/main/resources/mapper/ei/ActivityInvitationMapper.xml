<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ActivityInvitationMapper">
    <resultMap type="com.szhr.ei.domain.ActivityInvitation" id="ActivityInvitationResult">
        <result property="id"  column="id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="activityCompanyId"  column="activity_company_id"/>
        <result property="createdBy"  column="created_by"/>
        <result property="createDt"  column="create_dt"/>
        <result property="businessFeedback"  column="business_feedback"/>
        <result property="aiFeedback"  column="ai_feedback"/>
        <result property="state"  column="state"/>
        <result property="calledBy"  column="called_by"/>
        <result property="callDt"  column="call_dt"/>
        <result property="callResultNumber"  column="call_result_number"/>
        <result property="apiResult"  column="api_result"/>
        <result property="managedBy"  column="managed_by"/>
        <result property="lastUpdatedBy"  column="last_updated_by"/>
        <result property="lastUpdateDt"  column="last_update_dt"/>
        <result property="deletedBy"  column="deleted_by"/>
        <result property="deleteDt"  column="delete_dt"/>
    </resultMap>
    <resultMap id="QueryResult" type="com.szhr.ei.converter.response.ActivityInvitationVO" extends="ActivityInvitationResult">
        <result property="companyName"  column="company_name"/>
        <result property="industry"  column="industry"/>
        <result property="companyType"  column="company_type"/>
        <result property="companyProp"  column="company_prop"/>
        <result property="companyScale"  column="company_scale"/>
        <result property="contactPerson"  column="contact_person"/>
        <result property="mobilePhone"  column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="email"  column="email" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="createUserName"    column="create_user_name"    />
        <result property="manageUserName"    column="manage_user_name"    />
        <result property="industryName"    column="industry_name"    />
    </resultMap>
    <select id="selectActivityInvitationList" resultMap="QueryResult">
        select
        a.id,
        a.activity_id,
        a.activity_company_id,
        a.business_feedback,
        a.ai_feedback,
        a.state,
        a.called_by,
        a.call_dt,
        a.call_result_number,
        a.api_result,
        a.managed_by,
        a.created_by,
        a.create_dt,
        a.last_updated_by,
        a.last_update_dt,
        ac.company_name,
        ac.industry,
        ac.company_type,
        ac.company_prop,
        ac.company_scale,
        ac.contact_person,
        ac.mobile_phone,
        ac.email,
        su.user_name as create_user_name,
        mu.user_name as manage_user_name,
        dict.DICT_NAME as industry_name
        from activity_invitation a
        left join activity_company ac ON ac.id = a.activity_company_id
        left join sys_user su ON su.user_id = a.created_by
        left join sys_user mu ON mu.user_id = a.managed_by
        LEFT JOIN (SELECT DICT_CODE, DICT_NAME FROM data_dict_item WHERE DICT_TYPE_CODE = 'INDUSTRY') dict on ac.industry = dict.DICT_CODE
        where
            a.delete_dt is null
            <if test="activityId != null and activityId != ''">
                and a.activity_id = #{activityId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and a.created_by = #{createdBy}
            </if>
            <if test="managedBy != null and managedBy != ''">
                and a.managed_by = #{managedBy}
            </if>
            <if test="state != null and state != ''">
                and a.state = #{state}
            </if>
            <if test="businessFeedback != null and businessFeedback != ''">
                and a.business_feedback like concat('%',#{businessFeedback},'%')
            </if>
            <if test="aiFeedback != null and aiFeedback != ''">
                and a.ai_feedback like concat('%',#{aiFeedback},'%')
            </if>
            <if test="industry != null and industry != ''">
                and ac.industry = #{industry}
            </if>
            <if test="companyType != null and companyType != ''">
                and ac.company_type = #{companyType}
            </if>
            <if test="companyProp != null and companyProp != ''">
                and ac.company_prop = #{companyProp}
            </if>
            <if test="companyScale != null and companyScale != ''">
                and ac.company_scale = #{companyScale}
            </if>
            <if test="companyName != null and companyName != ''">
                and ac.company_name like concat('%',#{companyName},'%')
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                and ac.contact_person like concat('%',#{contactPerson},'%')
            </if>
            <if test="mobilePhone != null and mobilePhone != ''">
                and ac.mobile_phone = #{mobilePhone, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
            </if>
            <if test="email != null and email != ''">
                and ac.email = #{email, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
            </if>
        order by a.create_dt desc
    </select>

    <select id="listCompany" resultType="com.szhr.ei.converter.response.ActivityInvitationCompanyVO">
        select
        a.activity_id,
        a.activity_company_id,
        ac.company_name
        from activity_invitation a
        left join activity_company ac ON ac.id = a.activity_company_id
        where
        a.delete_dt is null
        <if test="activityId != null and activityId != ''">
            and a.activity_id = #{activityId}
        </if>
        <if test="companyName != null and companyName != ''">
            and ac.company_name like concat('%',#{companyName},'%')
        </if>
        order by a.create_dt desc
    </select>

    <select id="listOptionalCompany" resultType="com.szhr.ei.converter.response.ActivityInvitationOptionalCompanyVO">
        select
        ac.id as activity_company_id,
        ac.company_name
        from activity_invitation a
        right join activity_company ac ON (ac.id = a.activity_company_id and a.activity_id = #{activityId})
        where
        a.delete_dt is null and ac.delete_dt is null
        and a.id is null
        <if test="companyName != null and companyName != ''">
            and ac.company_name like concat('%',#{companyName},'%')
        </if>
        order by a.create_dt desc
    </select>
</mapper>
