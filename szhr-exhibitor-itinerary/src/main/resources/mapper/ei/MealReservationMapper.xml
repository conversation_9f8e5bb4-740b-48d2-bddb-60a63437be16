<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.MealReservationMapper">
    <resultMap type="com.szhr.ei.domain.MealReservation" id="MealReservationResult">
        <result property="mealReservationId"  column="meal_reservation_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="activityCompanyId" column="activity_company_id" />
        <result property="fairUserId"  column="fair_user_id"/>
        <result property="mealType" column="meal_type"/>
        <result property="mealDate" column="meal_date"/>
        <result property="mealOptionId"  column="meal_option_id"/>
        <result property="mealBoxId"  column="meal_box_id"/>
        <result property="createDt"  column="create_dt"/>
        <result property="boothCode"  column="booth_code"/>
        <result property="isSelfHelp"  column="is_self_help"/>
        <result property="status"  column="status"/>
        <result property="remarks"  column="remarks"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.MealReservationVO" id="MealReservationVOResult" extends="MealReservationResult">
        <result property="companyName" column="company_name" />
        <result property="restaurantName" column="restaurant_name" />
        <result property="mealTime" column="meal_time"/>
        <result property="boxName" column="box_name" />
        <result property="activityName" column="activity_name" />
        <result property="personName" column="person_name" />
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
    </resultMap>

    <select id="selectMealReservationVOList" parameterType="com.szhr.ei.converter.request.MealReservationQueryParams" resultMap="MealReservationVOResult">
        select aa.*, ai.activity_name,
        c.company_name  from
        (select mr.*, mo.restaurant_name, mo.meal_time, mb.box_name, a.activity_name, fu.person_name, fu.mobile_phone
        from meal_reservation mr
        left join meal_option mo on mr.meal_option_id = mo.meal_option_id
        left join meal_box mb on mr.meal_box_id = mb.meal_box_id
        left join activity a on mr.activity_id = a.id
        left join fair_user fu on mr.fair_user_id = fu.fair_user_id
        <where>
            <if test="status != null"> and mr.status = #{status} </if>
            <if test="mealReservationId != null and mealReservationId != ''"> and mr.meal_reservation_id = #{mealReservationId} </if>
            <if test="activityId != null and activityId != ''"> and mr.activity_id = #{activityId} </if>
            <if test="activityCompanyId != null and activityCompanyId != ''"> and mr.activity_company_id = #{activityCompanyId} </if>
            <if test="mealDate != null"> and DATE_FORMAT(mr.meal_date, '%Y-%m-%d') = DATE_FORMAT(#{mealDate} , '%Y-%m-%d')</if>
            <if test="mealType != null and mealType != ''"> and mr.meal_type = #{mealType} </if>
            <if test="activityName != null and activityName != ''"> and a.activity_name like concat('%', #{activityName}, '%') </if>
            <if test="personName != null and personName != ''"> and fu.person_name like concat('%', #{personName}, '%') </if>
            <if test="mobilePhone != null and mobilePhone != ''"> and fu.mobile_phone like concat('%', #{mobilePhone}, '%')</if>
            <if test="fairUserId != null and fairUserId != ''"> and fu.fair_user_id = {fairUserId} </if>
        </where>
        order by a.create_dt desc
        ) aa
        left join
        ( SELECT id as activity_id, activity_name FROM activity WHERE delete_dt IS NULL) ai
        ON aa.activity_id = ai.activity_id
        LEFT JOIN activity_company c
        ON aa.activity_company_id = c.id
        <where>
            <if test="companyName != null and companyName != ''"> and c.company_name like concat('%', #{companyName}, '%') </if>
        </where>
    </select>

    <resultMap type="com.szhr.ei.converter.dto.MealReservationDTO" id="MealReservationDTOResult">
        <result property="mealReservationId"  column="meal_reservation_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="activityCompanyId" column="activity_company_id" />
        <result property="fairUserId"  column="fair_user_id"/>
        <result property="mealType" column="meal_type"/>
        <result property="mealDate" column="meal_date"/>
        <result property="mealOptionId"  column="meal_option_id"/>
        <result property="mealBoxId"  column="meal_box_id"/>
    </resultMap>
    <select id="selectTimeoutNotChoosedMealReservationList" parameterType="com.szhr.ei.converter.dto.MealReservationDTO" resultMap="MealReservationDTOResult">
        select dd.* from
        (select fu.fair_user_id, fu.activity_company_id, cc.* from
            (
                SELECT mo.activity_id, mo.meal_type, mo.meal_date, mo.meal_option_id, bb.meal_box_id
                FROM meal_option mo
                LEFT JOIN (
                    select mb.*
                    from meal_box mb
                    inner join (
                    select meal_option_id, min(ranking) as ranking
                    from meal_box
                    group by meal_option_id
                ) aa on mb.meal_option_id = aa.meal_option_id and mb.ranking = aa.ranking
                ) bb ON mo.meal_option_id = bb.meal_option_id
                where mo.status = 1 and mo.meal_date &gt;= NOW() and mo.selection_end_time &lt; NOW()
            ) cc
            left join fair_user fu
            on cc.activity_id = fu.activity_id
            left join activity a
            on cc.activity_id = a.id
            <where>
                fu.delete_dt is null and a.delete_dt is null and a.end_date &gt;= NOW()
                <if test="mealType != null"> and cc.meal_type = #{mealType} </if>
                <if test="activityId != null and activityId != ''"> and cc.activity_id = #{activityId} </if>
            </where>
        ) dd
        left join
        (
            select mr.activity_id, mr.fair_user_id from meal_reservation mr
                <where>
                    mr.status = 1
                    <if test="mealType != null"> and mr.meal_type = #{mealType} </if>
                    <if test="activityId != null and activityId != ''"> and mr.activity_id = #{activityId} </if>
                </where>
        ) ee
        on dd.activity_id = ee.activity_id
        where ee.fair_user_id is null
        limit 100
    </select>

    <insert id="insertTimeoutNotChoosedMealReservationList" parameterType="java.util.List">
        insert into meal_reservation(meal_reservation_id, activity_id, activity_company_id, fair_user_id, meal_type, meal_date, meal_option_id, meal_box_id, create_dt, booth_code, is_self_help, status, remarks) values
        <foreach separator=","  collection="list" item="item">
            (#{item.mealReservationId}, #{item.activityId}, #{item.activityCompanyId}, #{item.fairUserId}, #{item.mealType}, #{item.mealDate}, #{item.mealOptionId}, #{item.mealBoxId}, #{item.createDt}, #{item.boothCode}, #{item.isSelfHelp}, #{item.status}, #{item.remarks})
        </foreach>
    </insert>
</mapper>
