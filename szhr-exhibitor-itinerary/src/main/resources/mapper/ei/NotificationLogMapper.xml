<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.NotificationLogMapper">
    <resultMap type="com.szhr.ei.domain.NotificationLog" id="NotificationLogResult">
        <result property="id"  column="id"/>
        <result property="fairUserId"  column="fair_user_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="type"  column="type"/>
        <result property="content"  column="content"/>
        <result property="status"  column="status"/>
        <result property="sentTime"  column="sent_time"/>
        <result property="createDt"  column="create_dt"/>
    </resultMap>
</mapper>
