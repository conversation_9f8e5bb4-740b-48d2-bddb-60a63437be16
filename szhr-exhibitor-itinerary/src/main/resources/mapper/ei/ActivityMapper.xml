<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ActivityMapper">
    <resultMap type="com.szhr.ei.domain.Activity" id="ActivityResult">
        <result property="id"  column="id"/>
        <result property="activityName"  column="activity_name"/>
        <result property="sponsor"  column="sponsor"/>
        <result property="undertaker"  column="undertaker"/>
        <result property="fairAddress"  column="fair_address"/>
        <result property="applyStartDate"  column="apply_start_date"/>
        <result property="applyEndDate"  column="apply_end_date"/>
        <result property="startDate"  column="start_date"/>
        <result property="endDate"  column="end_date"/>
        <result property="unionId"  column="union_id"/>
        <result property="logoLink"  column="logo_link" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler"/>
        <result property="managerPhone"  column="manager_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="createdBy"  column="created_by"/>
        <result property="createDt"  column="create_dt"/>
        <result property="lastUpdatedBy"  column="last_updated_by"/>
        <result property="lastUpdateDt"  column="last_update_dt"/>
        <result property="deletedBy"  column="deleted_by"/>
        <result property="deleteDt"  column="delete_dt"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.ActivityVO" id="ActivityDTOResult" extends="com.szhr.ei.mapper.ActivityMapper.ActivityResult">
        <result property="createUserName" column="create_user_name" />
        <result property="createNickName" column="create_nick_name" />
    </resultMap>

    <resultMap type = "com.szhr.ei.converter.response.ActivityQueryResponseFromUser" id="ActivityQueryResponseFromUserResult" extends="com.szhr.ei.mapper.ActivityMapper.ActivityResult">
            <result property ="fairUserId" column="fair_user_id" />
            <result property ="attachName" column="attach_name" />
            <result property ="attachCategory" column="attach_category" />
            <result property ="linkUrl" column="link_url" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler" />
    </resultMap>

    <select id="seclectActivityBasedOnFairUser" parameterType="com.szhr.ei.converter.request.ActivityQueryParams" resultMap="ActivityQueryResponseFromUserResult">
        select
        a.id, a.activity_name, a.sponsor, a.undertaker, a.fair_address, a.apply_start_date, a.apply_end_date,
        a.start_date, a.end_date, a.union_id, a.logo_link, a.manager_phone, a.created_by, a.create_dt,
        a.last_updated_by, a.last_update_dt, a.deleted_by, a.delete_dt, fu.fair_user_id,
        aa.link_url, aa.attach_name, aa.attach_category
        from activity a
        left join fair_user fu on fu.activity_id = a.id
        left join (select * from activity_attachment where attach_category = '会务手册' and activity_id = #{activityId} order by create_dt desc limit 1 ) aa on aa.activity_id = a.id
        <where>
            a.delete_dt is null
            <if test="fairUserId != null and fairUserId != ''">
                and fu.fair_user_id = #{fairUserId}
            </if>
            <if test="activityId != null and activityId != ''">
                and a.id = #{activityId}
            </if>
        </where>
        order by a.create_dt desc
    </select>

    <select id="selectActivityList" parameterType="com.szhr.ei.converter.request.ActivityParams" resultMap="ActivityDTOResult">
        select a.id, a.activity_name, a.sponsor, a.undertaker, a.fair_address, a.apply_start_date, a.apply_end_date, a.start_date, a.end_date, a.union_id, a.logo_link, a.manager_phone, a.created_by, a.create_dt, a.last_updated_by, a.last_update_dt,
               su.user_name as create_user_name, su.nick_name as create_nick_name
        from activity a
        left join sys_user su on su.user_id = a.created_by
        <where>
            a.delete_dt is null
            <if test="activityName != null  and activityName != ''">
                and a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="sponsor != null  and sponsor != ''">
                and a.sponsor like concat('%', #{sponsor}, '%')
            </if>
            <if test="undertaker != null  and undertaker != ''">
                and a.undertaker like concat('%', #{undertaker}, '%')
            </if>
            <choose>
                <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    and (
                    (date_format(a.start_date,'%Y-%m-%d') between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR} )
                    or (date_format(end_date,'%Y-%m-%d') between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR})
                    )
                </when >
                <when test="startDate != null and startDate != ''">
                    and date_format(a.start_date,'%Y-%m-%d') &gt;= #{startDate,jdbcType=VARCHAR}
                </when >
                <when test="endDate != null and endDate != ''">
                    and date_format(a.end_date,'%Y-%m-%d') &lt; #{endDate,jdbcType=VARCHAR}
                </when >
                <otherwise>
                </otherwise>
            </choose>
        </where>
        order by a.create_dt desc
    </select>
</mapper>