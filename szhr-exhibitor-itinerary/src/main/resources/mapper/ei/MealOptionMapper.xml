<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.MealOptionMapper">
    <resultMap type="com.szhr.ei.domain.MealOption" id="MealOptionResult">
        <result property="mealOptionId"  column="meal_option_id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="mealType"  column="meal_type"/>
        <result property="restaurantName"  column="restaurant_name"/>
        <result property="address"  column="address"/>
        <result property="mealDate"  column="meal_date"/>
        <result property="mealTime"  column="meal_time"/>
        <result property="photoUrl"  column="photo_url" typeHandler="com.szhr.common.utils.handler.mybatis.EncodeUrlHandler"/>
        <result property="maxPeople"  column="max_people"/>
        <result property="selectionStartTime"  column="selection_start_time"/>
        <result property="selectionEndTime"  column="selection_end_time"/>
        <result property="createdDt"  column="created_dt"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.MealOptionVO" id="MealOptionVOResult" extends="MealOptionResult">
        <result property="activityName" column="activity_name" />
    </resultMap>

    <select id="selectVOList" parameterType="com.szhr.ei.converter.request.MealOptionQueryParams" resultMap="MealOptionVOResult">
        select mo.*, a.activity_name
        from meal_option mo
        left join activity a on mo.activity_id = a.id
        <where>
            a.delete_dt is null
            <if test="activityName != null  and activityName != ''">
                and a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="mealDate != null"> and DATE_FORMAT(mo.meal_date, '%y-%m-%d') = DATE_FORMAT(#{mealDate}, '%y-%m-%d') </if>
            <if test="mealType != null"> and mo.meal_type = #{mealType} </if>
            <if test="status != null"> and mo.status = #{status} </if>
            <if test="activityId != null and activityId != ''"> and mo.activity_id = #{activityId} </if>
        </where>
        order by mo.create_dt desc
    </select>

    <select id="selectTimeoutNotChoosedMealOptionList" resultMap="MealOptionResult">
        select mo.* from meal_option mo
        left join activity a
        on mo.activity_id = a.id
        where mo.status = 1 and mo.meal_date &gt; NOW()
          and mo.selection_end_time &lt; NOW()
          and a.delete_dt is null and a.end_date &gt; NOW()
    </select>
</mapper>
