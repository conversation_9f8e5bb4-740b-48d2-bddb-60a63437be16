<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ActivityCompanyMapper">
    <resultMap type="com.szhr.ei.domain.ActivityCompany" id="ActivityCompanyResult">
        <result property="id"  column="id"/>
        <result property="companyName"  column="company_name"/>
        <result property="industry"  column="industry"/>
        <result property="companyType"  column="company_type"/>
        <result property="companyProp"  column="company_prop"/>
        <result property="companyScale"  column="company_scale"/>
        <result property="contactPerson"  column="contact_person"/>
        <result property="mobilePhone"  column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="email"  column="email" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
        <result property="createdBy"  column="created_by"/>
        <result property="createDt"  column="create_dt"/>
        <result property="lastUpdatedBy"  column="last_updated_by"/>
        <result property="lastUpdateDt"  column="last_update_dt"/>
        <result property="deleteDt"  column="delete_dt"/>
        <result property="deletedBy"  column="deleted_by"/>
    </resultMap>

    <resultMap id="queryMap" type="com.szhr.ei.converter.response.ActivityCompanyVO" extends="ActivityCompanyResult">
        <result property="createUserName"    column="create_user_name"    />
        <result property="industryName"    column="industry_name"    />
    </resultMap>

    <!--获取单位信息列表 -->
    <select id="selectActivityList" resultMap="queryMap">
        SELECT ac.id,
               ac.company_name,
               ac.industry,
               ac.company_type,
               ac.company_prop,
               ac.company_scale,
               ac.contact_person,
               ac.mobile_phone,
               ac.email,
               ac.created_by,
               ac.create_dt,
               ac.last_updated_by,
               ac.last_update_dt,
               u.user_name as create_user_name,
               dict.DICT_NAME as industry_name
        FROM
            activity_company ac
        LEFT JOIN
            sys_user u ON u.user_id = ac.created_by
        LEFT JOIN (SELECT DICT_CODE, DICT_NAME FROM data_dict_item WHERE DICT_TYPE_CODE = 'INDUSTRY') dict on ac.industry = dict.DICT_CODE
        <where>
            ac.delete_dt is null
            <if test="id != null and id != ''">
                and ac.id = #{id}
            </if>
            <if test="companyName != null and companyName != ''">
                and ac.company_name like concat('%',#{companyName},'%')
            </if>
            <if test="industry != null and industry != ''">
                and ac.industry = #{industry}
            </if>
            <if test="companyType != null and companyType != ''">
                and ac.company_type = #{companyType}
            </if>
            <if test="companyProp != null and companyProp != ''">
                and ac.company_prop = #{companyProp}
            </if>
            <if test="companyScale != null and companyScale != ''">
                and ac.company_scale = #{companyScale}
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                and ac.contact_person like concat('%',#{contactPerson},'%')
            </if>
            <if test="mobilePhone != null and mobilePhone != ''">
                and ac.mobile_phone = #{mobilePhone, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
            </if>
            <if test="email != null and email != ''">
                and ac.email = #{email, typeHandler=com.szhr.common.utils.handler.mybatis.AESTypeHandler}
            </if>
        </where>
        ORDER BY ac.create_dt DESC
    </select>
</mapper>
