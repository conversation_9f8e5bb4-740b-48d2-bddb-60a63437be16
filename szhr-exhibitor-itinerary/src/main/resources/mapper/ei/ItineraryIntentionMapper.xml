<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.ItineraryIntentionMapper">
    <resultMap type="com.szhr.ei.domain.ItineraryIntention" id="ItineraryIntentionResult">
        <result property="itineraryIntentionId" column="itinerary_intention_id"/>
        <result property="fairUserId" column="fair_user_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="ticketDirection" column="ticketDirection"/>
        <result property="travelMode" column="travel_mode"/>
        <result property="departureDate" column="departure_date"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createDt" column="create_dt"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap type="com.szhr.ei.converter.response.ItineraryIntentionVO" id="ItineraryIntentionVOResult" extends="ItineraryIntentionResult">
        <result property="activityCompanyId" column="activity_company_id" />
        <result property="companyName" column="company_name" />
        <result property="activityName" column="activity_name" />
        <result property="personName" column="person_name" />
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.szhr.common.utils.handler.mybatis.AESTypeHandler"/>
    </resultMap>

    <select id="selectItineraryIntentionVOList" parameterType="com.szhr.ei.converter.request.ItineraryIntentionQueryParams" resultMap="ItineraryIntentionVOResult">
        select aa.*, ac.company_name from
        (select ii.*, a.activity_name, fu.person_name, fu.mobile_phone
        from itinerary_intention ii
        left join activity a on ii.activity_id = a.id
        left join fair_user fu on ii.fair_user_id = fu.fair_user_id
        <where>
            <if test="activityId != null and activityId != ''"> and ii.activity_id = #{activityId} </if>
            <if test="ticketDirection != null and ticketDirection != ''"> and ii.ticket_direction = #{ticketDirection} </if>
            <if test="activityName != null and activityName != ''"> and a.activity_name like concat('%', #{activityName}, '%') </if>
            <if test="personName != null and personName != ''"> and fu.person_name like concat('%', #{personName}, '%') </if>
            <if test="mobilePhone != null and mobilePhone != ''"> and fu.mobile_phone like concat('%', #{mobilePhone}, '%')</if>
        </where>
        order by a.create_dt desc
        ) aa
        left join activity_company ac on aa.activity_company_id = ac.id
        <where>
            <if test="companyName != null and companyName != ''"> and ac.company_name like concat('%', #{companyName}, '%') </if>
        </where>
    </select>
</mapper>
