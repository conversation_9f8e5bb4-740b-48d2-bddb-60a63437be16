<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szhr.ei.mapper.StatisticsLogMapper">
    <resultMap type="com.szhr.ei.domain.StatisticsLog" id="StatisticsLogResult">
        <result property="id"  column="id"/>
        <result property="activityId"  column="activity_id"/>
        <result property="userType"  column="user_type"/>
        <result property="signupCount"  column="signup_count"/>
        <result property="mealCount"  column="meal_count"/>
        <result property="createdAt"  column="created_at"/>
    </resultMap>
</mapper>
